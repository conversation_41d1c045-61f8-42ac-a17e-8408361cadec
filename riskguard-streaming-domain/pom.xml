<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yeepay.ng.riskguard</groupId>
        <artifactId>riskguard-streaming-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>riskguard-streaming-domain</artifactId>
    <packaging>jar</packaging>

    <name>riskguard-streaming-domain</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yeepay.boot.components</groupId>
            <artifactId>yeepay-boot-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.boot.components</groupId>
            <artifactId>yeepay-boot-exception</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-rules-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-rules-facade</artifactId>
        </dependency>
    </dependencies>
</project>
