/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import com.yeepay.ng.riskguard.commons.func.BaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * title: 决策记录<br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/30 15:19
 */
@Data
public class DecideLog implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 指标的返回结果
     */
    private Map<String, Object> indicatorValues;

    /**
     * 命中规则
     */
    private List<RuleExecuteItem> hitRules;

    /**
     * 决策详情
     */
    private List<RuleExecuteItem> failedRules;

    /**
     * 命中的名单
     */
    private List<NameList> hitNameList;

    /**
     * 命中的名单规则
     */
    private List<RuleExecuteItem> hitNameListRules;

    /**
     * 系统变量
     */
    private Map<String, Object> variables;

    /**
     * 函数变量
     */
    private Map<String, BaseResponse> functionVariables;

    /**
     * 动作结果
     */
    private Map<RuleExecuteItem, Object> actionResult;


}
