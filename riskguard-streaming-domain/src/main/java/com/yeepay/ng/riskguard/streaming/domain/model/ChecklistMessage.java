package com.yeepay.ng.riskguard.streaming.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

import java.util.Map;
import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/22 11:36
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ChecklistMessage implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 业务板块
     */
    private String bizCode;


    /**
     * 业务场景
     */
    private String scenarioCode;

    /**
     * 业务阶段
     */
    private String stage;
    /**
     * 业务订单号，必须有值
     */
    private String requestNo;

    /**
     * 系统订单号
     */
    private String orderNo;

    /**
     * 核查结果，AT_RISK:有风险，NO_RISK:无风险，关联订单:NEED_RELATION，审核拒绝: AUDIT_REJECT
     */
    private String handleResult;

    /**
     * 调查信息
     */
    private String investigationInfo;

    /**
     * 核查单号
     */
    private String checklistNo;

    /**
     * 关联订单信息
     */
    private List<Map<String, Object>> relatedOrderList;
}
