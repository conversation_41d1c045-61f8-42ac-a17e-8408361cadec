package com.yeepay.ng.riskguard.streaming.domain.gateway.policy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import lombok.experimental.Tolerate;

import java.io.Serializable;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/12 18:35
 */
@Data
@AllArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@Accessors(chain = true)
@Builder
public class PolicyConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Tolerate
    public PolicyConfigDTO() {

    }

    /**
     * type：枚举值，BLACK(黑名单)/WHITE（白名单）/LIMIT（限额限次）
     */
    private String type;

    /**
     * 选中
     */
    private boolean checked;

}
