/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.repository;

import com.yeepay.ng.riskguard.streaming.domain.entity.RiskNotify;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/25 21:29
 */
public interface RiskNotifyRepository {

    Boolean create(RiskNotify notify);

    RiskNotify detail(String bizCode, String scenarioCode, String stage, String requestNo);

    Boolean update(RiskNotify notify);
}
