package com.yeepay.ng.riskguard.streaming.domain.gateway.decide;

import com.yeepay.ng.riskguard.commons.enums.EnvironmentType;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/2 11:09
 */
@Data
@Accessors(chain = true)
@ToString(exclude = "params")
public class DecideContextDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 业务板块
     */
    private String bizCode;

    /**
     * 业务场景
     */
    private String scenarioCode;

    /**
     * 阶段
     */
    private String stage;

    /**
     * 策略编码
     */
    private String policyCode;

    /**
     * 策略版本
     */
    private Long policyVersion;

    /**
     * 场景类型
     */
    private String scenarioType;

    /**
     * 是否跳过指标值查询
     */
    private Boolean skipIndicatorQuery;

    /**
     * 是否跳过名单引擎执行
     */
    private Boolean skipNameListEngine;

    /**
     * 是否跳过规则引擎执行
     */
    private Boolean skipRuleEngine;

    /**
     * 订单
     */
    private String orderNo;

    /**
     * 请求时间
     */
    private Date requestDate;

    /**
     * 执行参数上下文 包含指标值、业务方请求的参数 merchantNo（商编）、productId（产品id）、amount（下单金额）
     * 系统变量_开头,平台定义，与业务传的无关
     */
    private Map<String, Object> params;

    /**
     * 系统变量,与业务传的无关
     */
    private Map<String, Object> systemParams = new HashMap<>();

    /**
     * 指标
     */
    private Map<String, Object> indicators;

    private String environment = EnvironmentType.PRODUCT.name();

    /**
     * 最终决策结果
     */
    private DecideResultDTO finalDecide;
}
