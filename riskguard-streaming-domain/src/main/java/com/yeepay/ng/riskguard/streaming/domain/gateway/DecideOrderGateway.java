/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.gateway;


import com.yeepay.ng.riskguard.streaming.domain.entity.DecideOrder;
import com.yeepay.ng.riskguard.streaming.domain.entity.DecideOrderQuery;

/**
 * title: 决策订单网关<br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/3 10:44
 */
public interface DecideOrderGateway {
    /**
     * 根据主键标识查询决策订单
     *
     * @param query
     * @return
     */
    DecideOrder getDecideOrder(DecideOrderQuery query);
}