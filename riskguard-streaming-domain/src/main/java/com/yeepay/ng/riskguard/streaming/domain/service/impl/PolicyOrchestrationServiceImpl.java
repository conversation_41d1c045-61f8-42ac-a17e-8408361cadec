package com.yeepay.ng.riskguard.streaming.domain.service.impl;

import com.yeepay.ng.riskguard.streaming.domain.entity.Order;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderQueryReqest;
import com.yeepay.ng.riskguard.streaming.domain.entity.PolicyOrchestrationList;
import com.yeepay.ng.riskguard.streaming.domain.entity.PolicyOrchestrationRequest;
import com.yeepay.ng.riskguard.streaming.domain.repository.OrchestrationListRepository;
import com.yeepay.ng.riskguard.streaming.domain.repository.OrderRepository;
import com.yeepay.ng.riskguard.streaming.domain.service.PolicyOrchestrationService;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/2/25 6:15 下午
 */
@Component
public class PolicyOrchestrationServiceImpl implements PolicyOrchestrationService {
    @Setter(onMethod_ = @Autowired)
    private OrchestrationListRepository orchestrationListRepository;


    @Override
    public List<PolicyOrchestrationList> find(PolicyOrchestrationRequest request) {
        return orchestrationListRepository.find(request);
    }
}
