package com.yeepay.ng.riskguard.streaming.domain.entity;

import com.yeepay.ng.riskguard.commons.enums.EnvironmentType;
import lombok.*;

import java.io.Serializable;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/2/25 6:10 下午
 */
@Data
@Builder
public class PolicyOrchestrationRequest implements Serializable {

    private static final long serialVersionUID = -1L;
    private String policyCode;
    private Long policyVersion;
}
