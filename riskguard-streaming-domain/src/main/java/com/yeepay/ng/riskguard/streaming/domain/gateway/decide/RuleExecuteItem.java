/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.gateway.decide;

import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/25 18:12
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
@ToString
@Builder
public class RuleExecuteItem implements Serializable {
    private static final long serialVersionUID = -1L;
    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 规则版本
     */
    private String ruleVersion;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 执行匹配的时候是否失败
     */
    private Boolean matchFailed;

    /**
     * 执行匹配失败的错误信息
     */
    private String errorMessage;

    /**
     * 决策类型
     */
    private String decideType;

    /**
     * 待执行的动作
     */
    private List<String> actionCodes;
}
