package com.yeepay.ng.riskguard.streaming.domain.entity;

import com.yeepay.ng.riskguard.streaming.domain.model.RiskRuleExpression;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/2/25 11:11 上午
 */
@Data
@Builder
public class PolicyOrchestrationList implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键标识
     */
    private Long id;

    /**
     * 策略编码
     */
    private String policyCode;

    /**
     * 策略版本
     */
    private Integer policyVersion;

    /**
     * 业务板块编码
     */
    private String bizCode;

    /**
     * 业务场景编码
     */
    private String scenarioCode;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 阶段
     */
    private String stage;

    /**
     * 参数映射
     */

    private Map<String, Object> paramMapping;

    /**
     * 规则表达式集合
     */
    private List<RiskRuleExpression> expressions;

    /**
     * 值SYNC(同步执行)/ASYNC（异步执行)
     */
    private String executeMethod;

    /**
     * 优先级
     */
    private Byte priority;

    /**
     * 是否启用
     */
    private Byte enabled;

    /**
     * 备注
     */
    private String description;

    /**
     * 乐观锁
     */
    private Integer nonce;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 修改人
     */
    private String modifiedBy;

    /**
     * 修改时间
     */
    private Date modifiedAt;
}
