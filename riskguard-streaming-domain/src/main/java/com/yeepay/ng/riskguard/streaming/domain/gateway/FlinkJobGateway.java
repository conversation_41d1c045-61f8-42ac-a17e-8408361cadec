/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.gateway;


import com.yeepay.ng.riskguard.streaming.domain.entity.FlinkTable;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/3 14:09
 */
public interface FlinkJobGateway {
    /**
     * 获取动态表元数据，不存在则返回null
     *
     * @param tableName
     * @return
     */
    FlinkTable getTable(String tableName);
}