package com.yeepay.ng.riskguard.streaming.domain.gateway.decide;

import com.yeepay.ng.riskguard.commons.func.BaseResponse;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/2 11:09
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
@ToString
@Builder
public class DecideResultDTO implements Serializable {
    private static final long serialVersionUID = -1L;
    private String policyCode;
    private Long policyVersion;
    private String decideType;
    private Set<String> matchedRuleNames;
    private List<RuleExecuteItem> hitRules;
    private List<RuleExecuteItem> failedRules;

    /**
     * 函数变量
     */
    private Map<String, BaseResponse> functionVariables;
}
