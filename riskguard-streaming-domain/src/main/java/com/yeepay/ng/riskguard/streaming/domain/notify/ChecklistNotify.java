package com.yeepay.ng.riskguard.streaming.domain.notify;

import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/3/8 17:28
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
@ToString
@Builder
public class ChecklistNotify implements Serializable {

    private static final long serialVersionUID = -1L;


    /**
     * 业务板块
     */
    private String bizCode;


    /**
     * 业务场景
     */
    private String scenarioCode;

    /**
     * 业务阶段
     */
    private String stage;
    /**
     * 业务订单号，必须有值
     */
    private String requestNo;

    /**
     * 系统订单号
     */
    private String orderNo;

    /**
     * 核查结果，AT_RISK:有风险，NO_RISK:无风险，审核拒绝、需关联订单
     */
    private String handleResult;

    /**
     * 调查信息
     */
    private String investigationInfo;

    /**
     * 核查单号
     */
    private String checklistNo;

    /**
     * 关联订单信息
     */
    private List<Map<String, Object>> relatedOrderList;
}
