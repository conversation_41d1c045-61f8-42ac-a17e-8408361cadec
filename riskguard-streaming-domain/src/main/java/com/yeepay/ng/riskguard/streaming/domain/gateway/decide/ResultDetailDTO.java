package com.yeepay.ng.riskguard.streaming.domain.gateway.decide;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/2 11:09
 */
@Data
@Accessors(chain = true)
@ToString
public class ResultDetailDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 类型： BLOCK 阻断 ，PASS放行 CHECK 验证
     */
    private String type;

    /**
     * 决策的子配置
     * 如 决策类型为验证时子配置需要配置验证类型：SMS 短信，TEL电话，SMS$TEL 短信+电话
     */
    private String subConfig;

}
