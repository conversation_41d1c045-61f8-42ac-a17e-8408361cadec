/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import com.yeepay.ng.riskguard.commons.enums.OrderActualStatus;
import com.yeepay.ng.riskguard.commons.enums.OrderProcessStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * title: (风控)订单<br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/30 14:53
 */
@Setter
@Getter
@ToString(exclude = "requestParams")
public class Order implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * (风控系统生成的)订单号
     */
    private String orderNo;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 业务版本编码
     */
    private String bizCode;

    /**
     * 业务场景编码
     */
    private String scenarioCode;

    /**
     * 阶段
     */
    private String stage;

    /**
     * 业务系统的订单号（关联不同阶段的主键）
     */
    private String requestNo;

    /**
     * 请求时间
     */
    private Date requestAt;

    /**
     * 请求参数
     */
    private Map<String, Object> requestParams;

    /**
     * 环境
     */
    private String environment;

    /**
     * 策略编码
     */
    private String policyCode;

    /**
     * 策略版本
     */
    private Long policyVersion;

    /**
     * 决策类型
     */
    private String decideType;

    /**
     * 订单状态，INIT、PROCESSING，SUCCESS，FAILED
     */
    private OrderProcessStatus processStatus;

    /**
     * 交易状态
     */
    private OrderActualStatus actualStatus;

    /**
     * (处理失败时)错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 决策记录
     */
    private DecideLog decideLog;


    private int notifyStatus;

    private String notifyUrl;

    /**
     * 响应结果
     */
    private Map<String, Object> responseResult;

    /**
     * 核查单结果数据
     */
    private String checklistResult;
}