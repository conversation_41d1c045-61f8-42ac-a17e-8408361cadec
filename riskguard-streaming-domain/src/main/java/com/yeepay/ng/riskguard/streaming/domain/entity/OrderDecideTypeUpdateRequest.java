/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import lombok.Data;

import java.util.Date;

/**
 * title: 订单决策类型更新请求<br>
 * description: 用于封装订单决策类型更新的请求参数<br>
 * Copyright: Copyright (c)2024<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/04/02 14:51
 */
@Data
public class OrderDecideTypeUpdateRequest {
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 环境
     */
    private String environment;

    /**
     * 阶段
     */
    private String stage;

    /**
     * 请求时间
     */
    private Date requestAt;

    /**
     * 决策类型
     */
    private String decideType;
}
