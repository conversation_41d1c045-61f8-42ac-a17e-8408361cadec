/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.gateway;

import com.yeepay.ng.riskguard.rules.facade.indicator.dto.IndicatorDTO;

import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/25 14:27
 */
public interface IndicatorGateway {

    /**
     * 查询指标
     *
     * @param bizCode
     * @param dataModelCode
     * @param status
     * @return
     */
    List<IndicatorDTO> queryIndicators(String bizCode, String dataModelCode, String status);
}