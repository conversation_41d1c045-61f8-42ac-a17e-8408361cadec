/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Tolerate;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/1/2 16:54
 */
@Setter
@Getter
@Builder
@ToString
public class DecideOrderQuery {
    @Tolerate
    public DecideOrderQuery() {
    }

    /**
     * 业务板块
     */
    private String bizCode;

    /**
     * 业务场景
     */
    private String scenarioCode;

    /**
     * 请求流水号
     */
    private String requestNo;

    /**
     * 环境
     */
    private String environment;
}