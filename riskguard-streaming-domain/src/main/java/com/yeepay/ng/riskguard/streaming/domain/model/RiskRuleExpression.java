package com.yeepay.ng.riskguard.streaming.domain.model;

import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/17 10:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@Accessors(chain = true)
@Builder
public class RiskRuleExpression implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 表达式语言
     */
    private String language;

    /**
     * 表达式内容
     */
    private String content;
}
