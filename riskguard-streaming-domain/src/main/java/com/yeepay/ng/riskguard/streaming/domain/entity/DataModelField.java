/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * title: 数据模型字段<br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/3 10:54
 */
@Setter
@Getter
@ToString
public class DataModelField {
    /**
     * 编码
     */
    private String code;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 是否被作为主键
     */
    private Boolean asPrimaryKey;

    /**
     * 是否作为事件时间
     */
    private Boolean asEventTime;

    /**
     * 是否作为维度
     */
    private Boolean asDimension;

    /**
     * 是否作为度量
     */
    private Boolean asMeasure;
}