/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/27 10:39
 */
@Builder
@Data
public class RuleExecuteItem {
    @Tolerate
    public RuleExecuteItem() {

    }

    private String scope;
    private String ruleCode;
    private String ruleVersion;
    private Boolean enabled;
    private Boolean matchFailed;
    private String errorMessage;
    private String decideType;
    private List<String> actionCodes;
}