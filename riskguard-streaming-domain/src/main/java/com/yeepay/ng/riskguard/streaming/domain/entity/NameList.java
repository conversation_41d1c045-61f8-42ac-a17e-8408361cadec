/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/1/13 17:51
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NameList implements Serializable {
    private static final long serialVersionUID = -1L;
    /**
     * （名单）变量编码，即tbl_policy_namelist_config中的code
     */
    private String variableCode;

    /**
     * 名单库编码
     */
    private String code;

    /**
     * 名单类型：黑、白、灰
     */
    private String type;

    /**
     * 有效范围
     */
    private String effectiveScope;

    /**
     * 决策类型
     */
    private String decideType;

    /**
     * 命中的名单详情
     */
    private List<NameListDetail> details;

    /**
     * 名单上的分类
     */
    private List<String> categoryList;
}
