package com.yeepay.ng.riskguard.streaming.domain.repository;

import com.yeepay.ng.riskguard.streaming.domain.entity.Order;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderDecideLogUpdateRequest;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderDecideTypeUpdateRequest;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderQueryReqest;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderUpdateReqest;

import java.util.Date;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/26 11:32
 */
public interface OrderRepository {
    /**
     * 创建
     *
     * @param order
     * @param persistent
     */
    void create(Order order, boolean persistent);


    /**
     * 查询
     *
     * @param orderNo
     */
    Order findByOrderNo(String orderNo);

    /**
     * 查询
     * @param reqest
     * @return
     */
    Order find(OrderQueryReqest reqest);

    /**
     * 获取最新的订单信息
     * @param reqest
     * @return
     */
    Order findLastOrder(OrderQueryReqest reqest);

    /**
     * 更新状态
     * @param updateReqest
     */
    void updateStatus(OrderUpdateReqest updateReqest);

    /**
     * 更新核查单结果
     * @param updateReqest
     */
    void updateChecklistResult(OrderUpdateReqest updateReqest);

    /**
     * 更新订单决策类型
     *
     * @param request 决策类型更新请求
     * @return 是否更新成功
     */
    boolean updateDecideType(OrderDecideTypeUpdateRequest request);


    /**
     * 更新订单决策日志
     *
     * @param request 决策日志更新请求
     * @return 是否更新成功
     */
    boolean updateDecideLog(OrderDecideLogUpdateRequest request);
}
