/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/30 15:47
 */
@Data
@Accessors(chain = true)
public class RiskNotify implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    /**
     * 业务版本编码
     */
    private String bizCode;

    /**
     * 业务场景编码
     */
    private String scenarioCode;

    /**
     *
     */
    private String stage;

    /**
     * 业务系统的订单号（关联不同阶段的主键）
     */
    private String requestNo;


    /**
     * (风控系统生成的)订单号
     */
    private String orderNo;

    /**
     * 批次号
     */
    private Integer notifyStatus;


    /**
     * 环境
     */
    private String notifyUrl;


    /**
     * 请求时间
     */
    private Integer notifyTimes;

    /**
     * 核查单通知状态
     */
    private Integer checklistNotifyTimes;

    /**
     * 核查单通知次数
     */
    private Integer checklistNotifyStatus;


    /**
     * 创建时间
     */
    private Date createAt;
}