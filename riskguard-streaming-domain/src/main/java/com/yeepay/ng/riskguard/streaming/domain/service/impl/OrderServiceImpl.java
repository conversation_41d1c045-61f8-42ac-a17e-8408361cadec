/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.service.impl;

import com.yeepay.ng.riskguard.streaming.domain.entity.*;
import com.yeepay.ng.riskguard.streaming.domain.repository.OrderRepository;
import com.yeepay.ng.riskguard.streaming.domain.service.OrderService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/30 15:42
 */
@Component
@Slf4j
public class OrderServiceImpl implements OrderService {
    @Setter(onMethod_ = @Autowired)
    private OrderRepository orderRepository;

    @Override
    public void create(Order order, boolean persistent) {
        orderRepository.create(order, persistent);
    }

    @Override
    public Order findByOrderNo(String orderNo) {
        return null;
    }

    @Override
    public Order find(OrderQueryReqest reqest) {
        return orderRepository.find(reqest);
    }

    @Override
    public Order findLastOrder(OrderQueryReqest reqest) {
        return orderRepository.findLastOrder(reqest);
    }

    @Override
    public void updateStatus(OrderUpdateReqest updateReqest) {
        orderRepository.updateStatus(updateReqest);
    }

    @Override
    public void updateChecklistResult(OrderUpdateReqest updateReqest) {
        orderRepository.updateChecklistResult(updateReqest);
    }

    @Override
    public boolean updateDecideType(OrderDecideTypeUpdateRequest request) {
        log.info("更新订单决策类型: orderNo={}, environment={}, stage={}, decideType={}",
                request.getOrderNo(), request.getEnvironment(), request.getStage(), request.getDecideType());
        return orderRepository.updateDecideType(request);
    }


    @Override
    public boolean updateDecideLog(OrderDecideLogUpdateRequest request) {
        log.info("更新订单决策日志: orderNo={}, environment={}, stage={}",
                request.getOrderNo(), request.getEnvironment(), request.getStage());
        return orderRepository.updateDecideLog(request);
    }
}
