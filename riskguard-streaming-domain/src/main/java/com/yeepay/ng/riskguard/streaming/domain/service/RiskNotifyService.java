package com.yeepay.ng.riskguard.streaming.domain.service;

import com.yeepay.ng.riskguard.streaming.domain.entity.RiskNotify;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/1 16:12
 */
public interface RiskNotifyService {

    Boolean create(RiskNotify notify);

    RiskNotify detail(String bizCode, String scenarioCode, String stage, String requestNo);

    Boolean update(RiskNotify notify);
}
