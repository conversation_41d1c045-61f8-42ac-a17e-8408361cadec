package com.yeepay.ng.riskguard.streaming.domain.gateway.backend;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/9 14:25
 */
@Data
@Accessors(chain = true)
@ToString
public class RequestParamDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 场景编码
     * <p>
     * 预热阶段需要用到该字段。
     */
    private String scenarioCode;


    /**
     * 各阶段的参数配置
     * <p>
     * 预热阶段需要用到该字段。
     */
    private Map<String, List<ParamMappingDTO>> items;

}