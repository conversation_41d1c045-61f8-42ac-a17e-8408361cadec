package com.yeepay.ng.riskguard.streaming.domain.service.impl;

import com.yeepay.ng.riskguard.streaming.domain.entity.OrderControlChecklist;
import com.yeepay.ng.riskguard.streaming.domain.repository.OrderControlChecklistRepository;
import com.yeepay.ng.riskguard.streaming.domain.service.OrderControlChecklistService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/22 11:47
 */
@Component
@Slf4j
public class OrderControlChecklistServiceImpl implements OrderControlChecklistService {

    @Setter(onMethod_ = @Autowired)
    private OrderControlChecklistRepository checklistRepository;

    @Override
    public void create(OrderControlChecklist checklist) {
        checklistRepository.create(checklist);
    }
}