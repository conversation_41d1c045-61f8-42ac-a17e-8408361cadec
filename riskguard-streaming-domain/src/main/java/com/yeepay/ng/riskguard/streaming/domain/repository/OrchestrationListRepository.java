package com.yeepay.ng.riskguard.streaming.domain.repository;

import com.yeepay.ng.riskguard.streaming.domain.entity.PolicyOrchestrationList;
import com.yeepay.ng.riskguard.streaming.domain.entity.PolicyOrchestrationRequest;

import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/2/25 6:17 下午
 */
public interface OrchestrationListRepository {
    List<PolicyOrchestrationList> find(PolicyOrchestrationRequest reqest);
}
