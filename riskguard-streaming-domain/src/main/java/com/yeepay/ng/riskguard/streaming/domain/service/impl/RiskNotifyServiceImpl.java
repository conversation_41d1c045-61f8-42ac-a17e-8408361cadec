package com.yeepay.ng.riskguard.streaming.domain.service.impl;

import com.yeepay.ng.riskguard.streaming.domain.entity.RiskNotify;
import com.yeepay.ng.riskguard.streaming.domain.repository.RiskNotifyRepository;
import com.yeepay.ng.riskguard.streaming.domain.service.RiskNotifyService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/1 16:15
 */
@Service
@Slf4j
public class RiskNotifyServiceImpl implements RiskNotifyService {

    @Setter(onMethod_ = @Autowired)
    private RiskNotifyRepository notifyRepository;


    @Override
    public Boolean create(RiskNotify notify) {
        return notifyRepository.create(notify);
    }

    @Override
    public RiskNotify detail(String bizCode, String scenarioCode, String stage, String requestNo) {
        return notifyRepository.detail(bizCode, scenarioCode, stage, requestNo);
    }

    @Override
    public Boolean update(RiskNotify notify) {
        return notifyRepository.update(notify);
    }
}
