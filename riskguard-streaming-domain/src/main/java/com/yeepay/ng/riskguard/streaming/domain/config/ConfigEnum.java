package com.yeepay.ng.riskguard.streaming.domain.config;


import com.google.common.collect.Maps;
import com.yeepay.boot.components.config.ConfigKey;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/29 19:20
 */
public enum ConfigEnum implements ConfigKey {

    /**
     * decideType和老风控映射关系
     */
    RISKGUARD_DECIDE_TYPE_MAPPING("RISKGUARD_DECIDE_TYPE_MAPPING", new HashMap<String, String>() {
        {
            put("AT_RISK", "BLOCK");
            put("NO_RISK", "PASS");
            put("NEED_RELATION", "ORDER_VERIFY");
            put("AUDIT_REJECT", "AUDIT_REJECT");
        }
    }),
    ;

    private static Map<String, ConfigEnum> valueMap = Maps.newHashMap();

    static {
        for (ConfigEnum item : ConfigEnum.values()) {
            valueMap.put(item.configKey, item);
        }
    }

    private String configKey;
    private Object defaultValue;

    ConfigEnum(String configKey, Object defaultValue) {
        this.configKey = configKey;
        this.defaultValue = defaultValue;
    }

    public String getConfigKey() {
        return configKey;
    }

    public Object getDefaultValue() {
        return defaultValue;
    }

}
