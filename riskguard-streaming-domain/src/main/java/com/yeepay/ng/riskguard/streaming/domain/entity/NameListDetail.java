/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/1/9 11:48
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NameListDetail implements Serializable {
    private static final long serialVersionUID = -1L;
    /**
     * 名单数据id
     */
    private Long id;

    /**
     * 版本
     */
    private Long version;

    /**
     * 分类值
     * key:分类编码
     */
    private Map<String, List<String>> categoryValues;
}