/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/25 10:12
 */
@Getter
@AllArgsConstructor
public enum ExecuteMethod {
    /**
     * 同步执行
     */
    SYNC("SYNC", "同步执行"),



    /**
     * 异步执行
     */
    ASYNC("ASYNC", "异步执行");
    private final String method;
    private final String name;


}
