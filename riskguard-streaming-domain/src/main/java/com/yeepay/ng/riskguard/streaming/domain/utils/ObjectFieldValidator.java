package com.yeepay.ng.riskguard.streaming.domain.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/3/12 18:12
 */
public class ObjectFieldValidator {

    /**
     * 验证嵌套Map中是否存在所有必填字段
     * @param map 要验证的Map
     * @param requiredFields 必填字段的路径, 例如 "user.name", "user.address.city"
     * @return 缺失的字段列表，如果没有缺失则返回空列表
     */
    public static List<String> validateRequiredFields(Map<String, Object> map, Set<String> requiredFields) {
        List<String> missingFields = new ArrayList<>();

        for (String field : requiredFields) {
            if (!fieldExists(map, field)) {
                missingFields.add(field);
            }
        }

        return missingFields;
    }

    /**
     * 检查指定路径的字段是否存在且非null
     * @param map 要检查的Map
     * @param fieldPath 字段路径，例如 "user.name"
     * @return 字段是否存在且非null
     */
    private static boolean fieldExists(Map<String, Object> map, String fieldPath) {
        String[] parts = fieldPath.split("\\.");
        Object current = map;

        for (String part : parts) {
            if (current == null) {
                return false;
            }

            if (current instanceof Map) {
                Map<String, Object> currentMap = (Map<String, Object>) current;
                if (!currentMap.containsKey(part) || currentMap.get(part) == null) {
                    return false;
                }
                current = currentMap.get(part);
            } else {
                return false; // 当前节点不是Map，无法继续向下查找
            }
        }

        return true;
    }
}
