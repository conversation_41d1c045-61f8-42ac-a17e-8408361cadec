package com.yeepay.ng.riskguard.streaming.domain.gateway.backend;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/9 14:33
 */
@Data
@Accessors(chain = true)
@ToString
public class ParamMappingDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 参数名
     */
    private String name;


    private String parentCode;

    /**
     * 作为事件时间
     */
    private Boolean asEventTime;

    /**
     * 数据类型
     */
    private String type;

//    private String title;

//    private String description;

    /**
     * 是否必填字段
     */
    private Boolean required;

//    private Long sequence;

    private List<ParamMappingDTO> subFields;
}