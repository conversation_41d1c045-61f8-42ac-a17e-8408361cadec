/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.gateway.policy;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * title: Drools规则<br>
 * description: 每个策略版本对应一个Drools规则<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/8 18:57
 */
@Data
@Accessors(chain = true)
public class PolicyVersionDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * * 业务编码
     */
    private String bizCode;

    /**
     * 业务场景编码
     */
    private String scenarioCode;

    /**
     * 场景类型
     */
    private String scenarioType;

    /**
     *
     */
    private Map<String, List<PolicyVersionDetailDTO>> versions;


}