package com.yeepay.ng.riskguard.streaming.domain.gateway.policy;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * @version 1.0.0
 * <AUTHOR>
 * @since 2024/10/9 14:13
 */
@Data
@Accessors(chain = true)
@ToString
public class RiskPolicyDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    private String scenarioCode;

    private String name;

    private String code;

    private String description;

    private List<String> cumulations;

    /**
     * 配置
     */
    private List<PolicyConfigDTO> config;
}
