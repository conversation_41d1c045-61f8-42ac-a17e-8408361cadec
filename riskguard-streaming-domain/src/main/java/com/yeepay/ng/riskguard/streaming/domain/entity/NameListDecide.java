/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/1/9 11:24
 */
@Data
@Builder
@ToString
public class NameListDecide {
    /**
     * 决策类型
     */
    private String decideType;

    /**
     * 是否继续进行规则决策
     * 未命中名单或命中灰名单且决策类型不为BLOCK时继续规则决策
     */
    private Boolean continueRuleEngine;

    /**
     * 命中的名单
     */
    private List<NameList> nameList;
}