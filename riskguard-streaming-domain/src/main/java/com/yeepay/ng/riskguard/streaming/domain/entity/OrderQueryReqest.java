/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import com.yeepay.ng.riskguard.commons.enums.EnvironmentType;
import lombok.Data;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/12 15:37
 */
@Data
public class OrderQueryReqest implements Serializable {
    private static final long serialVersionUID = -1L;
    private String bizCode;
    private String scenarioCode;
    private String requestNo;
    private String stage;
    private String environment = EnvironmentType.PRODUCT.name();
}
