/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.gateway;

import com.yeepay.ng.riskguard.commons.valueobj.BizScenario;
import com.yeepay.ng.riskguard.streaming.domain.gateway.backend.RequestParamDTO;

import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/9 14:09
 */
public interface RequestParamGateway {
    /**
     * @param bizScenario 业务场景
     * @param stageType   阶段类型
     * @return
     */
    List<RequestParamDTO> query(BizScenario bizScenario, String stageType);
}