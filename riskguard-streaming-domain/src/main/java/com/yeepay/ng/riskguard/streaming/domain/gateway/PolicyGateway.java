/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.gateway;

import com.yeepay.ng.riskguard.commons.valueobj.BizScenario;
import com.yeepay.ng.riskguard.streaming.domain.gateway.policy.PolicyVersionDTO;
import com.yeepay.ng.riskguard.streaming.domain.gateway.policy.RiskPolicyDTO;

import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/9 14:09
 */
public interface PolicyGateway {
    /**
     * @param bizScenario 业务场景
     * @return
     */
    List<RiskPolicyDTO> query(BizScenario bizScenario);

    /**
     * 根据业务场景查询策略版本
     *
     * @param bizScenario
     * @param stage
     * @param environment
     * @return
     */
    List<PolicyVersionDTO> queryPolicyVersion(BizScenario bizScenario, String stage, String environment);
}