/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/25 10:12
 */
@Getter
@AllArgsConstructor
public enum WindowLevel {
    /**
     * 天
     */
    d(86400, "DAYS"),

    /**
     * 小时
     */
    H(3600, "HOURS"),

    /**
     * 分钟
     */
    m(60, "MINUTES"),

    /**
     * 秒
     */
    S(1, "SECONDS");
    private final int seconds;
    private final String flinkWindowUnit;


}