/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/3 10:47
 */
@Setter
@Getter
@ToString
public class DecideOrder {
    /**
     * 租户编码
     */
    private String tenant;

    /**
     * 业务板块
     */
    private String bizCode;

    /**
     * 业务场景
     */
    private String scenarioCode;

    /**
     * 订单号（风控系统生成的）
     */
    private String orderId;

    /**
     * 业务订单号
     */
    private String bizOrderId;

    /**
     * 是否异步
     */
    private Boolean async = Boolean.FALSE;

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * 业务参数
     */
    private Map<String, Object> params;

    /**
     * 决策类型
     */
    private String decideType;

    /**
     * 决策子类型
     */
    private String decideSubType;

    /**
     * 命中的规则
     */
    private List<String> hitRules;
}