/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/3 10:53
 */
@Setter
@Getter
@ToString
public class DataModel {
    /**
     * 业务板块
     */
    private String bizCode;

    /**
     * 数据模型编码
     */
    private String code;

    /**
     * 字段列表
     */
    private List<DataModelField> fields;
}