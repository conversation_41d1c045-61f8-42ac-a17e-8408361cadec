/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.gateway.policy;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * title: Drools规则<br>
 * description: 每个策略版本对应一个Drools规则<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/8 18:57
 */
@Data
@Accessors(chain = true)
public class PolicyVersionDetailDTO implements Serializable {

    private static final long serialVersionUID = -1L;


    /**
     * 场景阶段
     */
    private String stage;

    /**
     * 策略编码
     */
    private String policyCode;

    /**
     * 策略版本
     */
    private Long policyVersion;

    /**
     * 是否有指标
     */
    private Boolean hasIndicator;

    /**
     * 是否有名单
     */
    private Boolean hasNameList;

    /**
     * 是否有规则
     */
    private Boolean hasRules;

    /**
     * 规则分类
     */
    private List<String> ruleCategories;
}