/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.domain.entity;

import com.yeepay.ng.riskguard.commons.enums.OrderActualStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/12 15:51
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderUpdateReqest implements Serializable {
    private static final long serialVersionUID = -1L;
    private String bizCode;
    private String scenarioCode;
    private String requestNo;
    private OrderActualStatus actualStatus;
    /**
     * 响应结果
     */
    private Map<String, Object> responseResult;

    /**
     * 核查单结果数据
     */
    private String checklistResult;


    /**
     * 阶段
     */
    private String stage;
    /**
     * (风控系统生成的)订单号
     */
    private String orderNo;
}
