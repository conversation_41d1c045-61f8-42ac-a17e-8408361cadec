CREATE TABLE `tbl_order` (
    `order_no` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '订单号',
    `batch_no`       varchar(64) COLLATE utf8mb4_bin          DEFAULT '' COMMENT '批次号',
    `biz_code` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '业务板块编码',
    `scenario_code` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '业务场景编码',
    `stage`          varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT 'INQUIRY' COMMENT '阶段',
    `request_no`     varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '请求号',
    `request_at`     datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '请求时间',
    `request_params` json                                     DEFAULT NULL COMMENT '请求参数',
    `environment`    varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT 'PRODUCT' COMMENT '环境',
    `policy_code`    varchar(64) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '策略编码',
    `policy_version` int(11) DEFAULT '0' COMMENT '策略版本',
    `decide_type` varchar(16) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '决策类型',
    `process_status` tinyint(1) DEFAULT NULL COMMENT '订单状态，INIT、PROCESSING，SUCCESS，FAILED',
    `actual_status`  tinyint(1) DEFAULT NULL COMMENT '交易状态',
    `error_code`     varchar(64) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '错误编码',
    `error_msg`      varchar(256) COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '错误信息',
    `create_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    PRIMARY KEY (`request_at`, `order_no`, `environment`, `stage`) USING BTREE,
    UNIQUE KEY `uniq_request_no` (`biz_code`,`scenario_code`,`request_no`,`environment`,`stage`,`batch_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='订单';

CREATE TABLE `tbl_order_decide_log`
(
    `order_no`         varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '订单号',
    `stage`            varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT 'INQUIRY' COMMENT '阶段',
    `request_no`       varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '流水号',
    `request_at`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '请求时间',
    `environment`      varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT 'PRODUCT' COMMENT '环境',
    `hit_namelist`     json                                     DEFAULT NULL COMMENT '命中名单',
    `variables`        json                                     DEFAULT NULL COMMENT '系统变量',
    `indicator_values` json                                     DEFAULT NULL COMMENT '指标的返回结果',
    `hit_rules`        json                                     DEFAULT NULL COMMENT '命中规则',
    `failed_rules`     json                                     DEFAULT NULL COMMENT '未命中规则',
    PRIMARY KEY (`request_at`, `order_no`, `environment`, `stage`) USING BTREE,
    KEY                `idx_request_no` (`request_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='订单决策记录表';