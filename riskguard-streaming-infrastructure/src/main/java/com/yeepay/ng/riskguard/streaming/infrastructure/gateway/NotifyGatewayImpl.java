/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.gateway;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.ng.riskguard.streaming.domain.gateway.NotifyGateway;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/3/8 17:28
 */
@Component
@Slf4j
public class NotifyGatewayImpl implements NotifyGateway {

    private static final JsonMapper JSON_MAPPER = JsonMapper.nonNullMapper();

    private static final String SUCCESS = "SUCCESS";

    @Override
    public Boolean notify(String url, String content) {
        Boolean notifyStatus = Boolean.FALSE;
        log.info("notify url:{}, content:{}", url, content);
        HttpResponse response = null;
        try {
            response = HttpRequest.post(url).header("Content-Type", "application/json")
                    .body(content, "application/json")
                    .execute();
            log.info("notify result:{}", JSON_MAPPER.toJson(response));
        } catch (Exception e) {
            log.error("notify error", e);
        }

        if (null != response && response.isOk()) {
            String body = response.body();
            Map<String, Object> map = JSON_MAPPER.fromJson(body, Map.class);
            if (MapUtils.isNotEmpty(map)) {
                Object orDefault = map.getOrDefault("data", null);
                if (null != orDefault) {
                    String status = String.valueOf(orDefault);
                    if (SUCCESS.equals(status)) {
                        notifyStatus = Boolean.TRUE;
                    }
                }
            }
        }
        return notifyStatus;
    }
}
