package com.yeepay.ng.riskguard.streaming.infrastructure.handler;

import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.g3.utils.common.CollectionUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/12 16:56
 */

public abstract class ListTypeHandler<T> extends BaseTypeHandler<List<T>> {

    private JsonMapper JSON_MAPPER = JsonMapper.nonNullMapper();

    protected abstract Class<T> getType();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<T> parameter, JdbcType jdbcType) throws SQLException {
        String context = CollectionUtils.isEmpty(parameter) ? null : JSON_MAPPER.toJson(parameter);
        ps.setString(i, context);
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getResult(rs.getString(columnName));
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getResult(rs.getString(columnIndex));
    }

    @Override
    public List<T> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getResult(cs.getString(columnIndex));
    }

    /**
     * 根据json字符串格式化成List
     */
    private List<T> getResult(String context) {

        return JSON_MAPPER.fromJson(context, JSON_MAPPER.contructCollectionType(List.class, getType()));

    }

}
