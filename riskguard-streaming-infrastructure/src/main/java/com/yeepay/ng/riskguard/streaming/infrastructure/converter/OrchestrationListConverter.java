package com.yeepay.ng.riskguard.streaming.infrastructure.converter;

import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.streaming.domain.entity.Order;
import com.yeepay.ng.riskguard.streaming.domain.entity.PolicyOrchestrationList;
import com.yeepay.ng.riskguard.streaming.infrastructure.datavalue.OrderDO;
import com.yeepay.ng.riskguard.streaming.infrastructure.datavalue.PolicyOrchestrationListDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/2/25 6:32 下午
 */
@Mapper(imports = {
        java.util.Map.class
})
public interface OrchestrationListConverter {
    OrchestrationListConverter INSTANCE = Mappers.getMapper(OrchestrationListConverter.class);
    JsonMapper JSON_PARSER = JsonMapper.nonNullMapper();

    @Mappings({
            @Mapping(target = "paramMapping", expression = "java(JSON_PARSER.fromJson(entityDO.getParamMapping(), Map.class))")
    })
    PolicyOrchestrationList to(PolicyOrchestrationListDO entityDO);
    @Mappings({
            @Mapping(target = "paramMapping", expression = "java(JSON_PARSER.fromJson(entityDO.getParamMapping(), Map.class))")
    })
    List<PolicyOrchestrationList> to(List<PolicyOrchestrationListDO> entityDOList);

    @Mappings({
            @Mapping(target = "paramMapping", expression = "java(JSON_PARSER.toJson(entity.getParamMapping()))"),
    })
    PolicyOrchestrationListDO from(PolicyOrchestrationList entity);
    @Mappings({
            @Mapping(target = "paramMapping", expression = "java(JSON_PARSER.toJson(entity.getParamMapping()))"),
    })
    List<PolicyOrchestrationListDO> from(List<PolicyOrchestrationList> entityList);
}
