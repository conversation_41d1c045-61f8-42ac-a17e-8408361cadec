package com.yeepay.ng.riskguard.streaming.infrastructure.handler;


import com.yeepay.ng.riskguard.streaming.domain.model.RiskRuleExpression;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/17 10:51
 */
public class RiskRuleExpressionHandler extends ListTypeHandler{
    @Override
    protected Class<RiskRuleExpression> getType() {
        return RiskRuleExpression.class;
    }
}
