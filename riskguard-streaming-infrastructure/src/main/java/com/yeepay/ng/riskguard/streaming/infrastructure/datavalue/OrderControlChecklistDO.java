package com.yeepay.ng.riskguard.streaming.infrastructure.datavalue;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/22 11:50
 */
@Setter
@Getter
@TableName("tbl_order_control_checklist")
public class OrderControlChecklistDO {

    /**
     * 主键标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * (风控系统生成的)订单号
     */
    private String orderNo;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 业务版本编码
     */
    private String bizCode;

    /**
     * 业务场景编码
     */
    private String scenarioCode;

    /**
     * 阶段
     */
    private String stage;

    /**
     * 业务系统的订单号（关联不同阶段的主键）
     */
    private String requestNo;

    /**
     * 请求时间
     */
    private Date requestAt;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 核查单结果数据
     */
    private String checklistResult;

    /**
     * 调查信息
     */
    private String investigationInfo;

    /**
     * 核查单号
     */
    private String checklistNo;

    /**
     * 创建时间
     */
    private Date createAt;


    /**
     * 关联订单信息
     */
    private String relatedOrderList;

}
