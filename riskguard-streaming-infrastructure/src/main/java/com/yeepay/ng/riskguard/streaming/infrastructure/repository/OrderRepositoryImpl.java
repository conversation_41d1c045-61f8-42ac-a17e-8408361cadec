/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yeepay.ng.riskguard.commons.enums.EnvironmentType;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.streaming.domain.entity.Order;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderDecideLogUpdateRequest;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderDecideTypeUpdateRequest;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderQueryReqest;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderUpdateReqest;
import com.yeepay.ng.riskguard.streaming.domain.repository.OrderRepository;
import com.yeepay.ng.riskguard.streaming.infrastructure.converter.DecideLogConverter;
import com.yeepay.ng.riskguard.streaming.infrastructure.converter.OrderConverter;
import com.yeepay.ng.riskguard.streaming.infrastructure.datavalue.DecideLogDO;
import com.yeepay.ng.riskguard.streaming.infrastructure.datavalue.OrderDO;
import com.yeepay.ng.riskguard.streaming.infrastructure.mapper.DecideLogMapper;
import com.yeepay.ng.riskguard.streaming.infrastructure.mapper.OrderMapper;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/10 17:54
 */
@Component
@Slf4j
public class OrderRepositoryImpl implements OrderRepository {
    @Setter(onMethod_ = @Autowired)
    private OrderMapper orderMapper;

    @Setter(onMethod_ = @Autowired)
    private DecideLogMapper decideLogMapper;

    private static JsonParser jsonParser = JsonParserFactory.getJsonParser();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(Order order, boolean persistent) {
        if (persistent) {
            orderMapper.insert(OrderConverter.INSTANCE.from(order));
        }
        if (null != order.getDecideLog()) {
            DecideLogDO decideLogDO = DecideLogConverter.INSTANCE.from(order.getDecideLog());
            decideLogDO.setOrderNo(order.getOrderNo());
            decideLogDO.setRequestNo(order.getRequestNo());
            decideLogDO.setRequestAt(order.getRequestAt());
            decideLogDO.setEnvironment(order.getEnvironment());
            decideLogDO.setStage(order.getStage());
            decideLogMapper.insert(decideLogDO);
        }
    }

    @Override
    public Order findByOrderNo(String orderNo) {
        LambdaQueryWrapper<OrderDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrderDO::getOrderNo, orderNo);
        List<OrderDO> orders = orderMapper.selectList(queryWrapper);
        OrderDO orderDO = Optional.ofNullable(orders).orElse(new ArrayList<>()).stream().findFirst().orElse(null);
        return OrderConverter.INSTANCE.to(orderDO);
    }

    @Override
    public Order find(OrderQueryReqest reqest) {
        LambdaQueryWrapper<OrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(reqest.getBizCode()), OrderDO::getBizCode, reqest.getBizCode());
        queryWrapper.eq(StringUtils.isNotBlank(reqest.getScenarioCode()), OrderDO::getScenarioCode, reqest.getScenarioCode());
        queryWrapper.eq(StringUtils.isNotBlank(reqest.getRequestNo()), OrderDO::getRequestNo, reqest.getRequestNo());
        queryWrapper.eq(StringUtils.isNotBlank(reqest.getStage()), OrderDO::getStage, reqest.getStage());
        queryWrapper.eq(StringUtils.isNotBlank(reqest.getEnvironment()), OrderDO::getEnvironment, reqest.getEnvironment());
        return OrderConverter.INSTANCE.to(orderMapper.selectOne(queryWrapper));
    }

    @Override
    public Order findLastOrder(OrderQueryReqest reqest) {
        LambdaQueryWrapper<OrderDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(reqest.getRequestNo()), OrderDO::getRequestNo, reqest.getRequestNo());
        queryWrapper.eq(StringUtils.isNotBlank(reqest.getStage()), OrderDO::getStage, reqest.getStage());
        queryWrapper.orderByDesc(OrderDO::getRequestAt);
        java.util.List<Order> orders = OrderConverter.INSTANCE.to(orderMapper.selectList(queryWrapper));
        if (CollectionUtils.isNotEmpty(orders)) {
            return orders.get(0);
        }
        return null;

    }

    @Override
    public void updateStatus(OrderUpdateReqest updateReqest) {
        LambdaUpdateWrapper<OrderDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(null != updateReqest.getActualStatus(), OrderDO::getActualStatus, updateReqest.getActualStatus().getValue());
        updateWrapper.eq(StringUtils.isNotBlank(updateReqest.getBizCode()), OrderDO::getBizCode, updateReqest.getBizCode());
        updateWrapper.eq(StringUtils.isNotBlank(updateReqest.getScenarioCode()), OrderDO::getScenarioCode, updateReqest.getScenarioCode());
        updateWrapper.eq(StringUtils.isNotBlank(updateReqest.getRequestNo()), OrderDO::getRequestNo, updateReqest.getRequestNo());
        updateWrapper.eq(MapUtils.isNotEmpty(updateReqest.getResponseResult()), OrderDO::getResponseResult, jsonParser.toJson(updateReqest.getResponseResult()));
        orderMapper.update(null, updateWrapper);
    }

    @Override
    public void updateChecklistResult(OrderUpdateReqest updateReqest) {
        LambdaUpdateWrapper<OrderDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(null != updateReqest.getChecklistResult(), OrderDO::getChecklistResult, updateReqest.getChecklistResult());
        updateWrapper.eq(StringUtils.isNotBlank(updateReqest.getBizCode()), OrderDO::getBizCode, updateReqest.getBizCode());
        updateWrapper.eq(StringUtils.isNotBlank(updateReqest.getScenarioCode()), OrderDO::getScenarioCode, updateReqest.getScenarioCode());
        updateWrapper.eq(StringUtils.isNotBlank(updateReqest.getRequestNo()), OrderDO::getRequestNo, updateReqest.getRequestNo());
        updateWrapper.eq(StringUtils.isNotBlank(updateReqest.getStage()), OrderDO::getStage, updateReqest.getStage());
        updateWrapper.eq(StringUtils.isNotBlank(updateReqest.getOrderNo()), OrderDO::getOrderNo, updateReqest.getOrderNo());
        orderMapper.update(null, updateWrapper);
    }

    @Override
    public boolean updateDecideType(OrderDecideTypeUpdateRequest request) {
        if (StringUtils.isBlank(request.getOrderNo()) || StringUtils.isBlank(request.getEnvironment())
                || StringUtils.isBlank(request.getStage()) || request.getRequestAt() == null
                || StringUtils.isBlank(request.getDecideType())) {
            log.warn("更新订单决策类型失败: 必要参数为空");
            return false;
        }
        LambdaUpdateWrapper<OrderDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderDO::getOrderNo, request.getOrderNo())
                .eq(OrderDO::getEnvironment, request.getEnvironment())
                .eq(OrderDO::getStage, request.getStage())
                .eq(OrderDO::getRequestAt, request.getRequestAt());

        // 仅更新决策类型字段
        updateWrapper.set(OrderDO::getDecideType, request.getDecideType());
        // 执行更新
        int rows = orderMapper.update(null, updateWrapper);
        if (rows > 0) {
            log.debug("成功更新订单决策类型: orderNo={}, decideType={}", request.getOrderNo(), request.getDecideType());
            return true;
        } else {
            log.warn("未能更新订单决策类型: orderNo={}, decideType={}", request.getOrderNo(), request.getDecideType());
            return false;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDecideLog(OrderDecideLogUpdateRequest request) {
        if (request == null || request.getDecideLog() == null) {
            log.warn("更新订单决策日志失败: 请求对象或决策日志为空");
            return false;
        }

        if (StringUtils.isBlank(request.getOrderNo()) || StringUtils.isBlank(request.getEnvironment())
                || StringUtils.isBlank(request.getStage()) || request.getRequestAt() == null) {
            log.warn("更新订单决策日志失败: 必要参数为空");
            return false;
        }

        try {
            // 准备更新数据
            DecideLogDO decideLogDO = DecideLogConverter.INSTANCE.from(request.getDecideLog());
            // 直接构建更新条件进行更新
            LambdaUpdateWrapper<DecideLogDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(DecideLogDO::getOrderNo, request.getOrderNo())
                    .eq(DecideLogDO::getEnvironment, request.getEnvironment())
                    .eq(DecideLogDO::getStage, request.getStage())
                    .eq(DecideLogDO::getRequestAt, request.getRequestAt());

            // 只更新需要更新的字段
            if (request.getDecideLog().getIndicatorValues() != null) {
                updateWrapper.set(DecideLogDO::getIndicatorValues, decideLogDO.getIndicatorValues());
            }
            if (request.getDecideLog().getHitNameList() != null) {
                updateWrapper.set(DecideLogDO::getHitNameList, decideLogDO.getHitNameList());
            }
            if (request.getDecideLog().getVariables() != null) {
                updateWrapper.set(DecideLogDO::getVariables, decideLogDO.getVariables());
            }
            if (request.getDecideLog().getFunctionVariables() != null) {
                updateWrapper.set(DecideLogDO::getFunctionVariables, decideLogDO.getFunctionVariables());
            }
            if (request.getDecideLog().getHitRules() != null) {
                updateWrapper.set(DecideLogDO::getHitRules, decideLogDO.getHitRules());
            }
            if (request.getDecideLog().getHitNameListRules() != null) {
                updateWrapper.set(DecideLogDO::getHitNameListRules, decideLogDO.getHitNameListRules());
            }
            if (MapUtils.isNotEmpty(request.getDecideLog().getActionResult())) {
                updateWrapper.set(DecideLogDO::getActionResult, decideLogDO.getActionResult());
            }

            // 执行更新
            int rows = decideLogMapper.update(null, updateWrapper);

            if (rows > 0) {
                log.debug("成功更新订单决策日志: orderNo={}", request.getOrderNo());
                return true;
            } else {
                log.debug("更新订单决策表失败: orderNo={}", request.getOrderNo());
                return false;
            }
        } catch (Exception e) {
            log.error("更新订单决策日志发生异常: orderNo={}", request.getOrderNo(), e);
            return false;
        }
    }
}
