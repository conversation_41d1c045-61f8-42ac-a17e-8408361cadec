/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.gateway;

import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.ng.riskguard.rules.facade.indicator.dto.IndicatorDTO;
import com.yeepay.ng.riskguard.rules.facade.indicator.facade.IndicatorQueryFacade;
import com.yeepay.ng.riskguard.rules.facade.policy.facade.RiskPolicyQueryFacade;
import com.yeepay.ng.riskguard.streaming.domain.gateway.IndicatorGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/25 14:29
 */
@Component
@Slf4j
public class IndicatorGatewayImpl implements IndicatorGateway {
    private IndicatorQueryFacade indicatorQueryFacade() {
        return RemoteServiceFactory.getService(IndicatorQueryFacade.class);
    }

    @Override
    public List<IndicatorDTO> queryIndicators(String bizCode, String dataModelCode, String status) {
        log.info("queryIndicators bizCode:{}, dataModelCode:{}, status:{}", bizCode, dataModelCode, status);
        return indicatorQueryFacade().queryByDataModelCode(bizCode, dataModelCode, status);
    }
}