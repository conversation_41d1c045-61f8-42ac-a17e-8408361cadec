/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.factory;

import java.util.HashMap;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/12 15:04
 */
public class EventTypeHandleFactory {
    private static Map<String, EventTypeHandleStrategy> map = new HashMap<>();

    public static void register(EventTypeHandleStrategy eventTypeHandleStrategy) {
        map.put(eventTypeHandleStrategy.name(), eventTypeHandleStrategy);
    }

    public static EventTypeHandleStrategy get(String strategyName) {
        return map.get(strategyName);
    }
}
