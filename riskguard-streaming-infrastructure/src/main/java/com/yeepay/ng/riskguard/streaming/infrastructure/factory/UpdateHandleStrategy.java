package com.yeepay.ng.riskguard.streaming.infrastructure.factory;

import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.ng.riskguard.commons.enums.DecideType;
import com.yeepay.ng.riskguard.commons.enums.RulesetScope;
import com.yeepay.ng.riskguard.commons.func.BaseResponse;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.engine.facade.rule.dto.data.RuleExecuteItemDTO;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideRuleIdDTO;
import com.yeepay.ng.riskguard.gateway.facade.event.DecideFinishEvent;
import com.yeepay.ng.riskguard.streaming.domain.entity.*;
import com.yeepay.ng.riskguard.streaming.domain.service.OrderService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * title: 订单异步更新处理策略
 * description: 处理riskguard_order_async_update_event消息并更新订单状态
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/4/3 9:09 下午
 */
@Slf4j
@Component
public class UpdateHandleStrategy implements EventTypeHandleStrategy {

    private static final JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    @Setter(onMethod_ = @Autowired)
    private OrderService orderService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handle(String message) {
        try {
            log.info("接收到风控订单异步更新事件: {}", message);

            // 解析消息体
            //RiskGuardOrder riskGuardOrder = JSON_PARSER.parse(message, RiskGuardOrder.class);
            DecideFinishEvent decideFinishEvent = JSON_PARSER.parse(message, DecideFinishEvent.class);
            if (decideFinishEvent == null) {
                log.error("解析更新事件消息失败: {}", message);
                return;
            }

            // 查询已存在的订单信息
            String requestNo = decideFinishEvent.getRequestNo();
            String bizCode = decideFinishEvent.getBizCode();
            String scenarioCode = decideFinishEvent.getScenarioCode();
            String stage = decideFinishEvent.getStage();
            String environment = decideFinishEvent.getEnvironment();

            if (StringUtils.isBlank(requestNo) || StringUtils.isBlank(bizCode)
                    || StringUtils.isBlank(scenarioCode) || StringUtils.isBlank(environment)) {
                log.error("RiskGuardOrder中缺少必要字段: {}", decideFinishEvent);
                return;
            }

            // 订单查询
            OrderQueryReqest queryRequest = new OrderQueryReqest();
            queryRequest.setBizCode(bizCode);
            queryRequest.setScenarioCode(scenarioCode);
            queryRequest.setRequestNo(requestNo);
            queryRequest.setStage(stage);
            queryRequest.setEnvironment(environment);

            Order existingOrder = orderService.find(queryRequest);
            if (existingOrder == null) {
                log.error("未找到订单记录: bizCode={}, scenarioCode={}, requestNo={}, stage={}, environment={}",
                        bizCode, scenarioCode, requestNo, stage, environment);
                return;
            }

            // 比较决策类型优先级
            String newDecideType = decideFinishEvent.getDecideType();

            if (StringUtils.isBlank(newDecideType)) {
                log.info("新决策类型为空，保持现有决策类型不变");
                return;
            }

            // 更新订单状态
            updateOrderStatus(existingOrder, decideFinishEvent);

            // 更新订单的决策日志
            updateOrderDecideLog(existingOrder, decideFinishEvent);

            log.info("成功处理订单更新: requestNo={}", decideFinishEvent.getRequestNo());
        } catch (Exception e) {
            log.error("处理订单更新事件时发生错误", e);
        }
    }

    @Override
    public String name() {
        return "update";
    }

    @PostConstruct
    public void register() {
        EventTypeHandleFactory.register(this);
    }

    /**
     * 判断是否应该更新决策类型
     * 只有当新决策类型优先级高于现有决策类型时才更新
     *
     * @param existingDecideType 现有决策类型
     * @param newDecideType      新决策类型
     * @return 是否应该更新
     */
    private boolean shouldUpdateDecideType(String existingDecideType, String newDecideType) {
        if (StringUtils.isBlank(existingDecideType)) {
            return true;
        }

        try {
            DecideType existingType = EnumUtils.getEnum(DecideType.class, existingDecideType);
            DecideType newType = EnumUtils.getEnum(DecideType.class, newDecideType);

            if (existingType != null && newType != null) {
                // 使用枚举的优先级比较
                log.info("决策类型优先级比较 - 现有: {} (优先级: {}), 新: {} (优先级: {})",
                        existingDecideType, existingType.getPriority(), newDecideType, newType.getPriority());
                return newType.getPriority() > existingType.getPriority();
            }
        } catch (Exception e) {
            log.warn("决策类型比较失败，将使用默认比较逻辑: {}", e.getMessage());
        }

        // 如果失败，不更新
        return false;
    }

    /**
     * 更新订单状态信息
     *
     * @param existingOrder  已存在的订单
     * @param riskGuardOrder 新的订单信息
     */
    private void updateOrderStatus(Order existingOrder, DecideFinishEvent riskGuardOrder) {
        if (!Boolean.TRUE.equals(riskGuardOrder.getUpdateDecideType())) {
            log.warn("do not update decide type,requestNo:{}", riskGuardOrder.getRequestNo());
            return;
        }

        // 创建更新请求对象
        OrderDecideTypeUpdateRequest updateRequest = new OrderDecideTypeUpdateRequest();
        updateRequest.setOrderNo(existingOrder.getOrderNo());
        updateRequest.setEnvironment(existingOrder.getEnvironment());
        updateRequest.setStage(existingOrder.getStage());
        updateRequest.setRequestAt(existingOrder.getRequestAt());
        updateRequest.setDecideType(riskGuardOrder.getDecideType());

        // 使用OrderService更新订单决策类型
        boolean success = orderService.updateDecideType(updateRequest);

        if (success) {
            log.info("更新订单决策类型成功: orderNo={}, decideType={}",
                    existingOrder.getOrderNo(), riskGuardOrder.getDecideType());
        } else {
            log.warn("更新订单决策类型失败: orderNo={}, decideType={}",
                    existingOrder.getOrderNo(), riskGuardOrder.getDecideType());
        }
    }

    /**
     * 更新订单决策日志中的六个指定字段
     *
     * @param existingOrder  已存在的订单
     * @param riskGuardOrder 新的订单信息
     */
    private void updateOrderDecideLog(Order existingOrder, DecideFinishEvent riskGuardOrder) {
        if (riskGuardOrder == null) {
            log.info("未提供RiskGuardOrder数据，跳过决策日志更新");
            return;
        }

        // 构建新的决策日志实体
        DecideLog newDecideLog = new DecideLog();

        if (null != riskGuardOrder.getDecideLog() && MapUtils.isNotEmpty(riskGuardOrder.getDecideLog().getActionResult())) {
            Map<RuleExecuteItem, Object> actionResult = new HashMap<>();
            for (Map.Entry<DecideRuleIdDTO, Object> entry : riskGuardOrder.getDecideLog().getActionResult().entrySet()) {
                RuleExecuteItem ruleExecuteItem = RuleExecuteItem.builder()
                        .scope(entry.getKey().getScope().name())
                        .ruleCode(entry.getKey().getRuleCode())
                        .ruleVersion(String.valueOf(entry.getKey().getRuleVersion()))
                        .build();
                actionResult.put(ruleExecuteItem, entry.getValue());
            }
            newDecideLog.setActionResult(actionResult);
        }

        // 1. 指标返回结果 (indicatorValues)
        if (null != riskGuardOrder.getDecideLog() && MapUtils.isNotEmpty(riskGuardOrder.getDecideLog().getIndicatorValues())) {
            try {
                newDecideLog.setIndicatorValues(riskGuardOrder.getDecideLog().getIndicatorValues());
                log.debug("更新指标返回结果成功");
            } catch (Exception e) {
                log.warn("设置指标返回结果异常: {}", e.getMessage());
            }
        }

        // 2. 命中的名单 (hitNameList)
        if (null != riskGuardOrder.getDecideLog() && CollectionUtils.isNotEmpty(riskGuardOrder.getDecideLog().getHitNameList())) {
            try {
                newDecideLog.setHitNameList(JSON_PARSER.parse(JSON_PARSER.toJson(riskGuardOrder.getDecideLog().getHitNameList()),
                        JSON_PARSER.constructCollectionType(List.class, com.yeepay.ng.riskguard.streaming.domain.entity.NameList.class)));
                log.debug("更新命中名单成功");
            } catch (Exception e) {
                log.warn("设置命中名单异常: {}", e.getMessage());
            }
        }

        // 3. 系统变量 (variables)
        if (null != riskGuardOrder.getDecideLog() &&MapUtils.isNotEmpty(riskGuardOrder.getDecideLog().getVariables())) {
            try {
                newDecideLog.setVariables(riskGuardOrder.getDecideLog().getVariables());
                log.debug("更新系统变量成功");
            } catch (Exception e) {
                log.warn("设置系统变量异常: {}", e.getMessage());
            }
        }

        Map<String, BaseResponse> funcVariables = new HashMap<>();
        if (null != existingOrder.getDecideLog() && MapUtils.isNotEmpty(existingOrder.getDecideLog().getFunctionVariables())) {
            funcVariables.putAll(existingOrder.getDecideLog().getFunctionVariables());
        }
        if (null != riskGuardOrder.getDecideLog() &&MapUtils.isNotEmpty(newDecideLog.getFunctionVariables())) {
            for (String key : newDecideLog.getFunctionVariables().keySet()) {
                if (riskGuardOrder.getDecideLog().getFunctionVariables().get(key) != null) {
                    BaseResponse newResponse = riskGuardOrder.getDecideLog().getFunctionVariables().get(key);
                    if (!Boolean.TRUE.equals(newResponse.getAsync()) || !funcVariables.containsKey(key)) {
                        funcVariables.put(key, newResponse);
                    }
                }
            }
        }

        // 4. 函数变量 (functionVariables)
        if (null != riskGuardOrder.getDecideLog() &&MapUtils.isNotEmpty(riskGuardOrder.getDecideLog().getFunctionVariables())) {
            try {
                newDecideLog.setFunctionVariables(funcVariables);
                log.debug("更新函数变量成功");
            } catch (Exception e) {
                log.warn("设置函数变量异常: {}", e.getMessage());
            }
        }

        // 命中的规则
        if (null != riskGuardOrder.getDecideLog() &&CollectionUtils.isNotEmpty(riskGuardOrder.getDecideLog().getHitRules())) {
            List<RuleExecuteItem> executeItems = new ArrayList<>();
            for (RuleExecuteItemDTO dto : riskGuardOrder.getDecideLog().getHitRules()) {
                RuleExecuteItem item = new RuleExecuteItem();
                item.setDecideType(dto.getDecideType());
                item.setRuleCode(dto.getRuleCode());
                item.setRuleVersion(String.valueOf(dto.getRuleVersion()));
                item.setErrorMessage(dto.getErrorMessage());
                item.setMatchFailed(dto.getMatchFailed());
                item.setScope(RulesetScope.RULE.name());
                executeItems.add(item);
            }
            newDecideLog.setHitRules(executeItems);
        }
        // 命中的名单规则
        if (null != riskGuardOrder.getDecideLog() &&CollectionUtils.isNotEmpty(riskGuardOrder.getDecideLog().getHitNameListRules())) {
            List<RuleExecuteItem> nameListRules = new ArrayList<>();
            for (RuleExecuteItemDTO dto : riskGuardOrder.getDecideLog().getHitNameListRules()) {
                RuleExecuteItem item = new RuleExecuteItem();
                item.setRuleCode(dto.getRuleCode());
                item.setRuleVersion(String.valueOf(dto.getRuleVersion()));
                item.setDecideType(dto.getDecideType());
                nameListRules.add(item);
            }
            newDecideLog.setHitNameListRules(nameListRules);
        }

        // 创建更新请求对象
        OrderDecideLogUpdateRequest updateRequest = new OrderDecideLogUpdateRequest();
        updateRequest.setOrderNo(existingOrder.getOrderNo());
        updateRequest.setBizCode(existingOrder.getBizCode());
        updateRequest.setScenarioCode(existingOrder.getScenarioCode());
        updateRequest.setRequestNo(existingOrder.getRequestNo());
        updateRequest.setEnvironment(existingOrder.getEnvironment());
        updateRequest.setStage(existingOrder.getStage());
        updateRequest.setRequestAt(existingOrder.getRequestAt());
        updateRequest.setDecideLog(newDecideLog);

        // 直接更新四个字段
        boolean success = orderService.updateDecideLog(updateRequest);

        if (success) {
            log.info("更新订单决策日志成功: orderNo={}", existingOrder.getOrderNo());
        } else {
            log.warn("更新订单决策日志失败: orderNo={}", existingOrder.getOrderNo());
        }
    }
}
