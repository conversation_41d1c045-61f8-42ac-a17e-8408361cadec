/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.converter;

import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.streaming.domain.gateway.policy.PolicyVersionDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/30 15:58
 */
@Mapper
public interface PolicyVersionDTOConverter {
    PolicyVersionDTOConverter INSTANCE = Mappers.getMapper(PolicyVersionDTOConverter.class);

    JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    PolicyVersionDetailDTO to(com.yeepay.ng.riskguard.rules.facade.policy.dto.PolicyVersionDetailDTO orderDO);

    List<PolicyVersionDetailDTO> to(List<com.yeepay.ng.riskguard.rules.facade.policy.dto.PolicyVersionDetailDTO> orderDOList);

    com.yeepay.ng.riskguard.rules.facade.policy.dto.PolicyVersionDetailDTO from(PolicyVersionDetailDTO order);

    List<com.yeepay.ng.riskguard.rules.facade.policy.dto.PolicyVersionDetailDTO> from(List<PolicyVersionDetailDTO> orderDOList);
}