/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.gateway;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.streaming.domain.entity.DecideOrder;
import com.yeepay.ng.riskguard.streaming.domain.entity.DecideOrderQuery;
import com.yeepay.ng.riskguard.streaming.domain.gateway.DecideOrderGateway;
import com.yeepay.ng.riskguard.streaming.infrastructure.datavalue.OrderDO;
import com.yeepay.ng.riskguard.streaming.infrastructure.mapper.OrderMapper;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/3 14:15
 */
@Component
public class DecideOrderGatewayImpl implements DecideOrderGateway {
    @Setter(onMethod_ = @Autowired)
    private OrderMapper orderMapper;
    private static final JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    @Override
    public DecideOrder getDecideOrder(DecideOrderQuery query) {
        QueryWrapper<OrderDO> wrapper = Wrappers.query();
        wrapper.eq("biz_code", query.getBizCode());
        wrapper.eq("scenario_code",query.getScenarioCode());
        wrapper.eq("request_no", query.getRequestNo());
        wrapper.eq("environment",query.getEnvironment());
        wrapper.orderByDesc("request_at");
        wrapper.last("limit 1");
        return convert(orderMapper.selectOne(wrapper));
    }

    private DecideOrder convert(OrderDO orderDO) {
        if (orderDO == null) {
            return null;
        }
        DecideOrder decideOrder = new DecideOrder();
        decideOrder.setBizCode(orderDO.getBizCode());
        decideOrder.setScenarioCode(orderDO.getScenarioCode());
        decideOrder.setBizOrderId(orderDO.getBizCode());
        decideOrder.setOrderId(orderDO.getOrderNo());
        decideOrder.setParams(JSON_PARSER.parse(orderDO.getRequestParams(), Map.class));
        return decideOrder;
    }
}