package com.yeepay.ng.riskguard.streaming.infrastructure.repository;

import com.yeepay.ng.riskguard.streaming.domain.entity.OrderControlChecklist;
import com.yeepay.ng.riskguard.streaming.domain.repository.OrderControlChecklistRepository;
import com.yeepay.ng.riskguard.streaming.infrastructure.converter.OrderControlChecklistConverter;
import com.yeepay.ng.riskguard.streaming.infrastructure.mapper.OrderControlChecklistMapper;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/22 11:49
 */
@Component
@Slf4j
public class OrderControlChecklistRepositoryImpl implements OrderControlChecklistRepository {
    @Setter(onMethod_ = @Autowired)
    private OrderControlChecklistMapper orderControlChecklistMapper;

    @Override
    public void create(OrderControlChecklist checklist) {
        orderControlChecklistMapper.insert(OrderControlChecklistConverter.INSTANCE.from(checklist));
    }
}