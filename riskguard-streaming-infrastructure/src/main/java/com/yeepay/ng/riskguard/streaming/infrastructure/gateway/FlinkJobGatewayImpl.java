/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.gateway;

import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.ng.riskguard.rules.facade.job.dto.FlinkTableDTO;
import com.yeepay.ng.riskguard.rules.facade.job.facade.FlinkTableFacade;
import com.yeepay.ng.riskguard.streaming.domain.entity.FlinkTable;
import com.yeepay.ng.riskguard.streaming.domain.gateway.FlinkJobGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/3 14:12
 */
@Component
@Slf4j
public class FlinkJobGatewayImpl implements FlinkJobGateway {
    private FlinkTableFacade tableFacade() {
        return RemoteServiceFactory.getService(FlinkTableFacade.class);
    }

    @Override
    public FlinkTable getTable(String tableName) {
        try {
            FlinkTableDTO flinkTableDTO = tableFacade().query(tableName);
            if (flinkTableDTO == null) {
                return null;
            }
            return FlinkTable.builder()
                    .tableName(flinkTableDTO.getTableName())
                    .fields(flinkTableDTO.getFieldNames())
                    .build();
        } catch (Throwable e) {
            log.error("error happened when query flink table", e);
            return null;
        }

    }
}