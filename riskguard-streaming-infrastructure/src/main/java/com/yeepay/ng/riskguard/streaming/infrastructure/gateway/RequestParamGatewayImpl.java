/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.gateway;

import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.ng.riskguard.commons.valueobj.BizScenario;
import com.yeepay.ng.riskguard.rules.facade.policy.dto.RequestParamQuery;
import com.yeepay.ng.riskguard.rules.facade.policy.dto.RequestParamResult;
import com.yeepay.ng.riskguard.rules.facade.policy.facade.RequestParamFacade;
import com.yeepay.ng.riskguard.rules.facade.rule.dto.RequestParamItemDTO;
import com.yeepay.ng.riskguard.streaming.domain.gateway.RequestParamGateway;
import com.yeepay.ng.riskguard.streaming.domain.gateway.backend.ParamMappingDTO;
import com.yeepay.ng.riskguard.streaming.domain.gateway.backend.RequestParamDTO;
import com.yeepay.ng.riskguard.streaming.infrastructure.converter.RequestParamDTOConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: 统一配置工具类二次封装</p>
 * <p>Description: 描述</p>
 * <p>Copyright: Copyright (c)2011</p>
 * <p>Company: 易宝支付(YeePay)</p>
 *
 * <AUTHOR>
 * @version 0.1, 14-4-18 18:12
 */
@Component
@Slf4j
public class RequestParamGatewayImpl implements RequestParamGateway {

    private static final JsonMapper JSON_MAPPER = JsonMapper.nonNullMapper();

    private RequestParamFacade requestParamFacade() {
        return RemoteServiceFactory.getService(RequestParamFacade.class);
    }

    @Override
    public List<RequestParamDTO> query(BizScenario bizScenario, String stageType) {
        RequestParamQuery requestParam = RequestParamQuery.builder()
                .bizCode(bizScenario.getBizCode())
                .scenarioCode(bizScenario.getScenarioCode())
                .stage(stageType)
                .build();
        RequestParamResult params = null;
        List<RequestParamDTO> result = new ArrayList<>();
        try {
            log.info("查询请求参数配置，请求:{}", JSON_MAPPER.toJson(requestParam));
            params = requestParamFacade().query(requestParam);
            log.info("查询请求参数配置，响应:{}", JSON_MAPPER.toJson(params));
        } catch (Exception e) {
            log.error("查询风控求参数配置失败", e);
        }
        if (null != params && CollectionUtils.isNotEmpty(params.getRequestParams())) {
            params.getRequestParams().stream().forEach(item -> {
                RequestParamDTO dto = new RequestParamDTO();
                dto.setScenarioCode(item.getScenarioCode());
                Map<String, List<RequestParamItemDTO>> items = item.getItems();
                Map<String, List<ParamMappingDTO>> stageItems = new HashMap<>();
                for (Map.Entry<String, List<RequestParamItemDTO>> entry : items.entrySet()) {
                    String key = entry.getKey();
                    List<RequestParamItemDTO> value = entry.getValue();
                    stageItems.put(key, RequestParamDTOConverter.INSTANCE.from(value));
                }
                dto.setItems(stageItems);
                result.add(dto);
            });
        }
        return result;
    }
}
