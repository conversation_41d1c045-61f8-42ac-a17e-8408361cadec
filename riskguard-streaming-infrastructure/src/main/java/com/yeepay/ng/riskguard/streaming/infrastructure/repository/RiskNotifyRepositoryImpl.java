/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yeepay.ng.riskguard.streaming.domain.entity.Order;
import com.yeepay.ng.riskguard.streaming.domain.entity.RiskNotify;
import com.yeepay.ng.riskguard.streaming.domain.repository.RiskNotifyRepository;
import com.yeepay.ng.riskguard.streaming.infrastructure.converter.NotifyConverter;
import com.yeepay.ng.riskguard.streaming.infrastructure.converter.OrderConverter;
import com.yeepay.ng.riskguard.streaming.infrastructure.datavalue.RiskNotifyDO;
import com.yeepay.ng.riskguard.streaming.infrastructure.mapper.NotifyMapper;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/25 21:32
 */
@Component
public class RiskNotifyRepositoryImpl implements RiskNotifyRepository {

    @Setter(onMethod_ = @Autowired)
    private NotifyMapper notifyMapper;


    @Override
    public Boolean create(RiskNotify notify) {
        return notifyMapper.insert(NotifyConverter.INSTANCE.from(notify)) > 0;
    }

    @Override
    public RiskNotify detail(String bizCode, String scenarioCode, String stage, String requestNo) {
        LambdaQueryWrapper<RiskNotifyDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(requestNo), RiskNotifyDO::getRequestNo, requestNo);
        queryWrapper.eq(StringUtils.isNotBlank(bizCode),RiskNotifyDO::getBizCode, bizCode);
        queryWrapper.eq(StringUtils.isNotBlank(scenarioCode),RiskNotifyDO::getScenarioCode, scenarioCode);
        queryWrapper.eq(StringUtils.isNotBlank(stage),RiskNotifyDO::getStage, stage);
        java.util.List<RiskNotify> orders = NotifyConverter.INSTANCE.to(notifyMapper.selectList(queryWrapper));
        if (CollectionUtils.isNotEmpty(orders)) {
            return orders.get(0);
        }
        return null;
    }

    @Override
    public Boolean update(RiskNotify notify) {
        return notifyMapper.updateById(NotifyConverter.INSTANCE.from(notify)) > 0;
    }
}
