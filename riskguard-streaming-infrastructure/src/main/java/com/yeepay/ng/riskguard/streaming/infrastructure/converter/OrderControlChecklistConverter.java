package com.yeepay.ng.riskguard.streaming.infrastructure.converter;

import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderControlChecklist;
import com.yeepay.ng.riskguard.streaming.infrastructure.datavalue.OrderControlChecklistDO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/22 14:26
 */
@Mapper(imports = {
        java.util.List.class,
        java.lang.Object.class,
        java.util.Map.class,
        com.yeepay.ng.riskguard.commons.func.BaseResponse.class,
        StringUtils.class
})
public interface OrderControlChecklistConverter {
    OrderControlChecklistConverter INSTANCE = Mappers.getMapper(OrderControlChecklistConverter.class);
    JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();
    JsonMapper JSON_MAPPER = JsonMapper.nonNullMapper();

    @Mappings({
            @Mapping(target = "relatedOrderList", expression = "java(orderDO.getRelatedOrderList()!=null?JSON_MAPPER.fromJson(orderDO.getRelatedOrderList(),JSON_MAPPER.contructCollectionType(List.class,Map.class)):null)"),

    })
    OrderControlChecklist to(OrderControlChecklistDO orderDO);


    List<OrderControlChecklist> to(List<OrderControlChecklistDO> orderDOList);

    @Mappings({
            @Mapping(target = "relatedOrderList", expression = "java(order.getRelatedOrderList()!=null?JSON_PARSER.toJson(order.getRelatedOrderList()):null)"),
    })
    OrderControlChecklistDO from(OrderControlChecklist order);

    List<OrderControlChecklistDO> from(List<OrderControlChecklist> orderDOList);
}