package com.yeepay.ng.riskguard.streaming.infrastructure.converter;

import com.yeepay.ng.riskguard.rules.facade.policy.dto.RiskPolicyConfigDTO;
import com.yeepay.ng.riskguard.streaming.domain.gateway.policy.PolicyConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/11 15:42
 */
@Mapper
public interface PolicyConfigDTOConverter {

    PolicyConfigDTOConverter INSTANCE = Mappers.getMapper(PolicyConfigDTOConverter.class);

    PolicyConfigDTO to(RiskPolicyConfigDTO dto);

    List<PolicyConfigDTO> to(List<RiskPolicyConfigDTO> dtos);

    RiskPolicyConfigDTO from(PolicyConfigDTO queryDTO);

    List<RiskPolicyConfigDTO> from(List<PolicyConfigDTO> queryDTOs);
}
