package com.yeepay.ng.riskguard.streaming.infrastructure.converter;

import com.yeepay.ng.riskguard.rules.facade.rule.dto.RequestParamItemDTO;
import com.yeepay.ng.riskguard.streaming.domain.gateway.backend.ParamMappingDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/11 15:42
 */
@Mapper
public interface RequestParamDTOConverter {

    RequestParamDTOConverter INSTANCE = Mappers.getMapper(RequestParamDTOConverter.class);

    RequestParamItemDTO to(ParamMappingDTO dto);

    List<RequestParamItemDTO> to(List<ParamMappingDTO> dtos);

    ParamMappingDTO from(RequestParamItemDTO queryDTO);

    List<ParamMappingDTO> from(List<RequestParamItemDTO> queryDTOs);
}
