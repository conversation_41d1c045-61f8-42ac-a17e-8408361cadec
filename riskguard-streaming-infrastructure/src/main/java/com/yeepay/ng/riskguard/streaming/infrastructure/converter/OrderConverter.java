/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.converter;

import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.streaming.domain.entity.Order;
import com.yeepay.ng.riskguard.streaming.infrastructure.datavalue.OrderDO;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/30 15:58
 */
@Mapper(
        imports = {MapUtils.class, StringUtils.class}
)
public interface OrderConverter {
    OrderConverter INSTANCE = Mappers.getMapper(OrderConverter.class);
    JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    @Mappings({
            @Mapping(target = "requestParams", expression = "java(JSON_PARSER.parse(orderDO.getRequestParams(),java.util.Map.class))"),
            @Mapping(target = "actualStatus", expression = "java(null != orderDO.getActualStatus()?com.yeepay.ng.riskguard.commons.enums.OrderActualStatus.parse(orderDO.getActualStatus()):null)"),
            @Mapping(target = "processStatus", expression = "java(null != orderDO.getProcessStatus()?com.yeepay.ng.riskguard.commons.enums.OrderProcessStatus.parse(orderDO.getProcessStatus()):null)"),
            @Mapping(target = "responseResult", expression = "java(StringUtils.isEmpty(orderDO.getResponseResult())?null:JSON_PARSER.parse(orderDO.getResponseResult(),java.util.Map.class))"),
    })
    Order to(OrderDO orderDO);

    List<Order> to(List<OrderDO> orderDOList);

    @Mappings({
            @Mapping(target = "requestParams", expression = "java(JSON_PARSER.toJson(order.getRequestParams()))"),
            @Mapping(target = "actualStatus", expression = "java(null == order.getActualStatus()?null:order.getActualStatus().getValue())"),
            @Mapping(target = "processStatus", expression = "java(null == order.getProcessStatus()?null:order.getProcessStatus().getValue())"),
            @Mapping(target = "responseResult", expression = "java(MapUtils.isEmpty(order.getResponseResult())?null:JSON_PARSER.toJson(order.getResponseResult()))"),
    })
    OrderDO from(Order order);

    List<OrderDO> from(List<Order> orderDOList);
}