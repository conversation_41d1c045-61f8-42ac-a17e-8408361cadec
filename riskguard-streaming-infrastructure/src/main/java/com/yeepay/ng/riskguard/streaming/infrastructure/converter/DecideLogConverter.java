/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.converter;

import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.streaming.domain.entity.DecideLog;
import com.yeepay.ng.riskguard.streaming.infrastructure.datavalue.DecideLogDO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/30 16:01
 */
@Mapper(imports = {
        java.util.List.class,
        java.lang.Object.class,
        java.util.Map.class,
        com.yeepay.ng.riskguard.commons.func.BaseResponse.class,
        StringUtils.class
})
public interface DecideLogConverter {
    DecideLogConverter INSTANCE = Mappers.getMapper(DecideLogConverter.class);
    JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    @Mappings({
            @Mapping(target = "hitRules",expression = "java(JSON_PARSER.parse(decideLogDO.getHitRules(),JSON_PARSER.constructCollectionType(List.class, com.yeepay.ng.riskguard.streaming.domain.entity.RuleExecuteItem.class)))"),
            @Mapping(target = "failedRules",expression = "java(JSON_PARSER.parse(decideLogDO.getFailedRules(),JSON_PARSER.constructCollectionType(List.class, com.yeepay.ng.riskguard.streaming.domain.entity.RuleExecuteItem.class)))"),
            @Mapping(target = "indicatorValues", expression = "java(JSON_PARSER.parse(decideLogDO.getIndicatorValues(),JSON_PARSER.constructMapType(Map.class, String.class, Object.class)))"),
            @Mapping(target = "variables", expression = "java(JSON_PARSER.parse(decideLogDO.getVariables(),JSON_PARSER.constructMapType(Map.class, String.class, Object.class)))"),
            @Mapping(target = "hitNameList", expression = "java(JSON_PARSER.parse(decideLogDO.getHitNameList(),JSON_PARSER.constructCollectionType(List.class, com.yeepay.ng.riskguard.streaming.domain.entity.NameList.class)))"),
            @Mapping(target = "hitNameListRules", expression = "java(JSON_PARSER.parse(decideLogDO.getHitNameListRules(),JSON_PARSER.constructCollectionType(List.class, com.yeepay.ng.riskguard.streaming.domain.entity.RuleExecuteItem.class)))"),
            @Mapping(target = "functionVariables", expression = "java(JSON_PARSER.parse(decideLogDO.getFunctionVariables(),JSON_PARSER.constructMapType(Map.class, String.class, com.yeepay.ng.riskguard.commons.func.BaseResponse.class)))"),
            @Mapping(target = "actionResult", expression = "java(StringUtils.isEmpty(decideLogDO.getActionResult())?null:JSON_PARSER.parse(decideLogDO.getActionResult(),Map.class))")
    })
    DecideLog to(DecideLogDO decideLogDO);

    List<DecideLog> to(List<DecideLogDO> decideLogDOList);

    @Mappings({
            @Mapping(target = "hitRules", expression = "java(null!=decideLog.getHitRules()?JSON_PARSER.toJson(decideLog.getHitRules()):null)"),
            @Mapping(target = "failedRules", expression = "java(null!=decideLog.getFailedRules()?JSON_PARSER.toJson(decideLog.getFailedRules()):null)"),
            @Mapping(target = "indicatorValues", expression = "java(null!=decideLog.getIndicatorValues()?JSON_PARSER.toJson(decideLog.getIndicatorValues()):null)"),
            @Mapping(target = "variables", expression = "java(null!=decideLog.getVariables()?JSON_PARSER.toJson(decideLog.getVariables()):null)"),
            @Mapping(target = "hitNameList", expression = "java(null!=decideLog.getHitNameList()?JSON_PARSER.toJson(decideLog.getHitNameList()):null)"),
            @Mapping(target = "hitNameListRules", expression = "java(null!=decideLog.getHitNameListRules()?JSON_PARSER.toJson(decideLog.getHitNameListRules()):null)"),
            @Mapping(target = "functionVariables", expression = "java(null!=decideLog.getFunctionVariables()?JSON_PARSER.toJson(decideLog.getFunctionVariables()):null)"),
            @Mapping(target = "actionResult", expression = "java(null!=decideLog.getActionResult()?JSON_PARSER.toJson(decideLog.getActionResult()):null)")
    })
    DecideLogDO from(DecideLog decideLog);

    List<DecideLogDO> from(List<DecideLog> decideLogDOList);
}
