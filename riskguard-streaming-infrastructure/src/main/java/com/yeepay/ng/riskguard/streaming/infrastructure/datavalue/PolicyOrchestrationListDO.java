package com.yeepay.ng.riskguard.streaming.infrastructure.datavalue;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yeepay.ng.riskguard.streaming.domain.model.RiskRuleExpression;
import com.yeepay.ng.riskguard.streaming.infrastructure.handler.RiskRuleExpressionHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/2/25 11:11 上午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tbl_risk_scenario_orchestration",autoResultMap = true)
public class PolicyOrchestrationListDO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 策略编码
     */
    @TableField("policy_code")
    private String policyCode;

    /**
     * 策略版本
     */
    @TableField("policy_version")
    private Integer policyVersion;

    /**
     * 业务板块编码
     */
    @TableField("biz_code")
    private String bizCode;

    /**
     * 业务场景编码
     */
    @TableField("scenario_code")
    private String scenarioCode;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 阶段
     */
    @TableField("stage")
    private String stage;

    /**
     * 参数映射
     */

    @TableField("param_mapping")
    private String paramMapping;

    /**
     * 规则表达式集合
     */

    @TableField(typeHandler = RiskRuleExpressionHandler.class, value = "`expressions`")
    private List<RiskRuleExpression> expressions;

    /**
     * 值SYNC(同步执行)/ASYNC（异步执行)
     */
    @TableField("execute_method")
    private String executeMethod;

    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 是否启用
     */
    @TableField("enabled")
    private Integer enabled;

    /**
     * 备注
     */
    @TableField("description")
    private String description;

    /**
     * 乐观锁
     */
    @TableField("nonce")
    private Integer nonce;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("create_at")
    private Date createAt;

    /**
     * 修改人
     */
    @TableField("modified_by")
    private String modifiedBy;

    /**
     * 修改时间
     */
    @TableField("modified_at")
    private Date modifiedAt;
}
