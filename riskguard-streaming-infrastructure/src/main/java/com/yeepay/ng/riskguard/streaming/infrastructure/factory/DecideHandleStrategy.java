/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.factory;

import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.ng.riskguard.commons.enums.OrderActualStatus;
import com.yeepay.ng.riskguard.commons.enums.OrderProcessStatus;
import com.yeepay.ng.riskguard.commons.enums.RulesetScope;
import com.yeepay.ng.riskguard.commons.enums.StageType;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.commons.utils.OrderNoUtils;
import com.yeepay.ng.riskguard.engine.facade.namelist.dto.NameListDTO;
import com.yeepay.ng.riskguard.engine.facade.namelist.dto.NameListDetailDTO;
import com.yeepay.ng.riskguard.engine.facade.rule.dto.data.RuleExecuteItemDTO;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideRuleIdDTO;
import com.yeepay.ng.riskguard.gateway.facade.event.DecideFinishEvent;
import com.yeepay.ng.riskguard.streaming.domain.entity.*;
import com.yeepay.ng.riskguard.streaming.domain.service.OrderService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/12 15:11
 */
@Slf4j
@Component
public class DecideHandleStrategy implements EventTypeHandleStrategy {
    private static final JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Setter(onMethod_ = @Autowired)
    private OrderService orderService;

    @Override
    public void handle(String message) {
        DecideFinishEvent decideFinishEvent = null;
        try {
            decideFinishEvent = JSON_PARSER.parse(message, DecideFinishEvent.class);
        } catch (Exception e) {
            log.error("JSON解析失败，message：{}", message);
            return;
        }
        if (null == decideFinishEvent) {
            log.info("decideFinishEvent is null, message:{}", message);
            return;
        }
        log.info("receive create order event, requestNo:{}, orderNo:{}, environment:{}, persistent:{}", decideFinishEvent.getRequestNo(), decideFinishEvent.getOrderNo(), decideFinishEvent.getEnvironment(), decideFinishEvent.getPersistent());
        orderService.create(buildOrder(decideFinishEvent), null == decideFinishEvent.getPersistent() ? true : decideFinishEvent.getPersistent());
    }

    @Override
    public String name() {
        return "decide";
    }

    @PostConstruct
    public void register() {
        EventTypeHandleFactory.register(this);
    }

    private Order buildOrder(DecideFinishEvent decideFinishEvent) {
        Order order = new Order();
        order.setResponseResult(decideFinishEvent.getResponseResult());
        order.setOrderNo(decideFinishEvent.getOrderNo());
        order.setBatchNo(decideFinishEvent.getBatchNo());
        order.setEnvironment(decideFinishEvent.getEnvironment());
        order.setRequestAt(OrderNoUtils.parseRequestAt(decideFinishEvent.getOrderNo().substring(0, 14)));
        order.setRequestNo(decideFinishEvent.getRequestNo());
        order.setBizCode(decideFinishEvent.getBizCode());
        order.setScenarioCode(decideFinishEvent.getScenarioCode());
        order.setRequestParams(decideFinishEvent.getParams());
        order.setDecideType(decideFinishEvent.getDecideType());
        order.setActualStatus(EnumUtils.getEnum(OrderActualStatus.class, decideFinishEvent.getActualStatus()));
        order.setProcessStatus(EnumUtils.getEnum(OrderProcessStatus.class, decideFinishEvent.getProcessStatus()));
        order.setErrorMsg(decideFinishEvent.getErrorMsg());
        order.setPolicyCode(decideFinishEvent.getPolicyCode());
        order.setPolicyVersion(decideFinishEvent.getPolicyVersion());
        order.setStage(StageType.INQUIRY.name());
        order.setNotifyStatus(decideFinishEvent.getNotifyStatus());
        order.setNotifyUrl(decideFinishEvent.getNotifyUrl());

        DecideLog decideLog = new DecideLog();
        if (null != decideFinishEvent.getDecideLog() && MapUtils.isNotEmpty(decideFinishEvent.getDecideLog().getActionResult())) {
            Map<RuleExecuteItem, Object> actionResult = new HashMap<>();
            for (Map.Entry<DecideRuleIdDTO, Object> entry : decideFinishEvent.getDecideLog().getActionResult().entrySet()) {
                RuleExecuteItem ruleExecuteItem = RuleExecuteItem.builder()
                        .scope(entry.getKey().getScope().name())
                        .ruleCode(entry.getKey().getRuleCode())
                        .ruleVersion(String.valueOf(entry.getKey().getRuleVersion()))
                        .build();
                actionResult.put(ruleExecuteItem, entry.getValue());
            }
            decideLog.setActionResult(actionResult);
        }

        decideLog.setIndicatorValues(decideFinishEvent.getDecideLog().getIndicatorValues());
        decideLog.setFailedRules(convertRules(decideFinishEvent.getDecideLog().getFailedRules()));
        decideLog.setHitRules(convertRules(decideFinishEvent.getDecideLog().getHitRules()));
        decideLog.setVariables(decideFinishEvent.getDecideLog().getVariables());
        decideLog.setHitNameList(convertNameLists(decideFinishEvent.getDecideLog().getHitNameList()));
        decideLog.setFunctionVariables(decideFinishEvent.getDecideLog().getFunctionVariables());
        decideLog.setHitNameListRules(convertNameListRules(decideFinishEvent.getDecideLog().getHitNameListRules()));
        order.setDecideLog(decideLog);
        return order;
    }

    private List<RuleExecuteItem> convertNameListRules(List<RuleExecuteItemDTO> hitNameListRules) {
        if (CollectionUtils.isEmpty(hitNameListRules)) {
            return Collections.emptyList();
        }
        List<RuleExecuteItem> result = new ArrayList<>();
        for (RuleExecuteItemDTO nameListRuleDTO : hitNameListRules) {
            result.add(RuleExecuteItem.builder()
                    .ruleCode(nameListRuleDTO.getRuleCode())
                    .ruleVersion(String.valueOf(nameListRuleDTO.getRuleVersion()))
                    .decideType(nameListRuleDTO.getDecideType())
                    .build());
        }
        return result;
    }

    private List<NameList> convertNameLists(List<NameListDTO> nameLists) {
        if (CollectionUtils.isEmpty(nameLists)) {
            return Collections.emptyList();
        }
        List<NameList> result = new ArrayList<>();
        for (NameListDTO nameListDTO : nameLists) {
            result.add(NameList.builder()
                    .code(nameListDTO.getCode())
                    .type(nameListDTO.getType())
                    .effectiveScope(CollectionUtils.isEmpty(nameListDTO.getEffectiveScope()) ? null : nameListDTO.getEffectiveScope().get(0))
                    .decideType(nameListDTO.getDecideType())
                    .details(convertNameListDetails(nameListDTO.getDetails()))
                    .variableCode(nameListDTO.getVariableCode())
                    .categoryList(nameListDTO.getCategoryList())
                    .build());
        }
        return result;
    }

    private List<NameListDetail> convertNameListDetails(List<NameListDetailDTO> details) {
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
        List<NameListDetail> result = new ArrayList<>();
        for (NameListDetailDTO detail : details) {
            result.add(NameListDetail.builder()
                    .id(detail.getId())
                    .version(detail.getVersion())
                    .categoryValues(detail.getCategoryValues())
                    .build());
        }
        return result;
    }

    private List<RuleExecuteItem> convertRules(List<RuleExecuteItemDTO> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return Collections.EMPTY_LIST;
        }
        List<RuleExecuteItem> result = new ArrayList<>();
        for (RuleExecuteItemDTO rule : rules) {
            result.add(RuleExecuteItem.builder()
                    .ruleCode(rule.getRuleCode())
                    .ruleVersion(String.valueOf(rule.getRuleVersion()))
                    .matchFailed(rule.getMatchFailed())
                    .enabled(rule.getEnabled())
                    .decideType(rule.getDecideType())
                    .scope(RulesetScope.RULE.name())
                    .build());

        }
        return result;
    }
}
