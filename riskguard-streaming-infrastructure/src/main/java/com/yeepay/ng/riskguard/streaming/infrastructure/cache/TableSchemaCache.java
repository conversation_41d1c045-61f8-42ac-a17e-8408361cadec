/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.cache;

import com.yeepay.ng.riskguard.commons.cache.AbstractCacheRefreshAfterAccess;
import com.yeepay.ng.riskguard.commons.cache.YopCacheLoader;
import com.yeepay.ng.riskguard.streaming.domain.entity.FlinkTable;
import com.yeepay.ng.riskguard.streaming.domain.gateway.FlinkJobGateway;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * title: 源表元数据本地缓存<br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/3 15:29
 */
@Component
public class TableSchemaCache extends AbstractCacheRefreshAfterAccess<String, FlinkTable> {
    @Setter(onMethod_ = @Autowired)
    private FlinkJobGateway flinkJobGateway;

    @Override
    public YopCacheLoader<String, FlinkTable> getCacheLoader() {
        return new YopCacheLoader<String, FlinkTable>() {

            @Override
            protected FlinkTable doLoad(String tableName) throws Exception {
                return flinkJobGateway.getTable(tableName);
            }

            @Override
            protected String getCacheName() {
                return "TableSchema-Cache";
            }
        };
    }

    @Override
    public long refreshAfterAccess() {
        // 12 小时
        return 12 * 60 * 60;
    }

    @Override
    public long maximumSize() {
        return 500;
    }
}