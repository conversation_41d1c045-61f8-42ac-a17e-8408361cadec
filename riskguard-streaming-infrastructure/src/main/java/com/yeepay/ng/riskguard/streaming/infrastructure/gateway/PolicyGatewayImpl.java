/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.gateway;


import cn.hutool.core.map.MapUtil;
import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.ng.riskguard.commons.valueobj.BizScenario;
import com.yeepay.ng.riskguard.rules.facade.policy.dto.PolicyQuery;
import com.yeepay.ng.riskguard.rules.facade.policy.dto.PolicyQueryResult;
import com.yeepay.ng.riskguard.rules.facade.policy.dto.RiskPolicyQuery;
import com.yeepay.ng.riskguard.rules.facade.policy.dto.RiskPolicyResult;
import com.yeepay.ng.riskguard.rules.facade.policy.facade.RiskPolicyQueryFacade;
import com.yeepay.ng.riskguard.streaming.domain.gateway.PolicyGateway;
import com.yeepay.ng.riskguard.streaming.domain.gateway.policy.PolicyVersionDTO;
import com.yeepay.ng.riskguard.streaming.domain.gateway.policy.PolicyVersionDetailDTO;
import com.yeepay.ng.riskguard.streaming.domain.gateway.policy.RiskPolicyDTO;
import com.yeepay.ng.riskguard.streaming.infrastructure.converter.PolicyConfigDTOConverter;
import com.yeepay.ng.riskguard.streaming.infrastructure.converter.PolicyVersionDTOConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <p>Title: 风控策略累加查询</p>
 * <p>Description: 描述</p>
 * <p>Copyright: Copyright (c)2011</p>
 * <p>Company: 易宝支付(YeePay)</p>
 *
 * <AUTHOR>
 * @version 0.1
 */
@Slf4j
@Component
public class PolicyGatewayImpl implements PolicyGateway {

    private static JsonMapper JSON_MAPPER = JsonMapper.nonNullMapper();

    private RiskPolicyQueryFacade policyQueryFacade() {
        return RemoteServiceFactory.getService(RiskPolicyQueryFacade.class);
    }

    @Override
    public List<RiskPolicyDTO> query(BizScenario bizScenario) {
        RiskPolicyQuery query = RiskPolicyQuery.builder().bizCode(bizScenario.getBizCode())
                .scenarioCode(bizScenario.getScenarioCode()).build();
        RiskPolicyResult riskPolicyResult = null;
        List<RiskPolicyDTO> result = new ArrayList<>();
        try {
            log.info("查询风控策略累加规则，请求:{}", query);
            riskPolicyResult = policyQueryFacade().queryPolicy(query);
            //log.info("查询风控策略累加规则，响应:{}", JSON_MAPPER.toJson(riskPolicyResult));
        } catch (Exception e) {
            log.error("查询风控策略累加规则失败", e);
        }
        if (null != riskPolicyResult && CollectionUtils.isNotEmpty(riskPolicyResult.getRiskPolices())) {
            riskPolicyResult.getRiskPolices().stream().forEach(item -> {
                RiskPolicyDTO riskPolicyDTO = new RiskPolicyDTO();
                riskPolicyDTO.setCode(item.getCode())
                        .setScenarioCode(item.getScenarioCode())
                        .setName(item.getName())
                        .setCode(item.getCode())
                        .setCumulations(item.getCumulations())
                        .setConfig(PolicyConfigDTOConverter.INSTANCE.to(item.getConfig()));
                result.add(riskPolicyDTO);
            });
        }
        return result;
    }

    @Override
    public List<PolicyVersionDTO> queryPolicyVersion(BizScenario bizScenario, String stage, String environment) {
        PolicyQuery query = PolicyQuery.builder().bizCode(bizScenario.getBizCode()).scenarioCode(bizScenario.getScenarioCode()).stage(stage).environment(environment).build();
        PolicyQueryResult queryResult;
        List<PolicyVersionDTO> versions = new ArrayList<>();
        try {
            log.info("查询风控策略版本，请求:{}", query);
            queryResult = policyQueryFacade().query(query);
            if (null == queryResult || CollectionUtils.isEmpty(queryResult.getVersions())) {
                log.warn("policy version is empty,query:{}", query);
                return Collections.EMPTY_LIST;
            }
        } catch (Exception e) {
            log.error("查询风控策略版本失败", e);
            return Collections.EMPTY_LIST;
        }
        queryResult.getVersions().stream().forEach(item -> {
            PolicyVersionDTO dto = new PolicyVersionDTO();
            dto.setScenarioType(item.getType());
            String scenarioCode = item.getScenarioCode();
            Map<String, List<com.yeepay.ng.riskguard.rules.facade.policy.dto.PolicyVersionDetailDTO>> version = item.getVersions();
            dto.setScenarioCode(scenarioCode);
            Map<String, List<PolicyVersionDetailDTO>> versionDetail = new HashMap<>();
            if (MapUtil.isNotEmpty(version)) {
                for (Map.Entry<String, List<com.yeepay.ng.riskguard.rules.facade.policy.dto.PolicyVersionDetailDTO>> entry : version.entrySet()) {
                    List<com.yeepay.ng.riskguard.rules.facade.policy.dto.PolicyVersionDetailDTO> value = entry.getValue();
                    versionDetail.put(entry.getKey(), PolicyVersionDTOConverter.INSTANCE.to(value));
                }
            }
            dto.setVersions(versionDetail);
            versions.add(dto);
        });

        return versions;
    }
}