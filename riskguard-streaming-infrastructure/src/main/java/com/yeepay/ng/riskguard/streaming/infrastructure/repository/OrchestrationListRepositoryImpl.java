package com.yeepay.ng.riskguard.streaming.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yeepay.ng.riskguard.streaming.domain.entity.PolicyOrchestrationList;
import com.yeepay.ng.riskguard.streaming.domain.entity.PolicyOrchestrationRequest;
import com.yeepay.ng.riskguard.streaming.domain.repository.OrchestrationListRepository;
import com.yeepay.ng.riskguard.streaming.infrastructure.converter.OrchestrationListConverter;
import com.yeepay.ng.riskguard.streaming.infrastructure.datavalue.PolicyOrchestrationListDO;
import com.yeepay.ng.riskguard.streaming.infrastructure.mapper.OrchestrationListMapper;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/2/25 6:23 下午
 */
@Component
public class OrchestrationListRepositoryImpl implements OrchestrationListRepository {
    @Setter(onMethod_ = @Autowired)
    private OrchestrationListMapper orchestrationListMapper;

    @Override
    public List<PolicyOrchestrationList> find(PolicyOrchestrationRequest reqest) {
        LambdaQueryWrapper<PolicyOrchestrationListDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(reqest.getPolicyCode()), PolicyOrchestrationListDO::getPolicyCode, reqest.getPolicyCode());
        queryWrapper.eq(reqest.getPolicyVersion() != null, PolicyOrchestrationListDO::getPolicyVersion, reqest.getPolicyVersion());
        return OrchestrationListConverter.INSTANCE.to(orchestrationListMapper.selectList(queryWrapper));
    }
}
