/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.converter;

import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yeepay.ng.riskguard.streaming.infrastructure.datavalue.RiskNotifyDO;
import com.yeepay.ng.riskguard.streaming.domain.entity.RiskNotify;

import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/30 15:58
 */
@Mapper
public interface NotifyConverter {
    NotifyConverter INSTANCE = Mappers.getMapper(NotifyConverter.class);
    JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    RiskNotify to(RiskNotifyDO notifyDO);

    List<RiskNotify> to(List<RiskNotifyDO> notifyDOS);

    RiskNotifyDO from(RiskNotify notify);

    List<RiskNotifyDO> from(List<RiskNotify> notifies);
}