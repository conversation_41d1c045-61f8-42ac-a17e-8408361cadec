/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.datavalue;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/30 15:47
 */
@Setter
@Getter
@TableName("tbl_order_decide_log")
public class DecideLogDO {
    /**
     * (风控系统生成的)订单号
     */
    private String orderNo;

    /**
     * 阶段
     */
    private String stage;

    /**
     * 请求号
     */
    private String requestNo;

    /**
     * 请求时间
     */
    private Date requestAt;

    /**
     * 指标的返回结果
     */
    private String indicatorValues;

    /**
     * 命中规则
     */
    private String hitRules;

    /**
     * 决策详情
     */
    private String failedRules;

    /**
     * 环境
     */
    private String environment;

    /**
     * 系统变量
     */
    private String variables;

    /**
     * 命中的名单详情
     */
    @TableField("hit_namelist")
    private String hitNameList;

    /**
     * 命中的名单规则
     */
    @TableField("hit_namelist_rules")
    private String hitNameListRules;

    /**
     * 函数变量
     */
    private String functionVariables;

    private String actionResult;
}
