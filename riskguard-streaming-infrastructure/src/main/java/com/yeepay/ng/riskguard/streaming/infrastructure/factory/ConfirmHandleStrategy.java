/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.infrastructure.factory;

import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.ng.riskguard.commons.enums.OrderActualStatus;
import com.yeepay.ng.riskguard.commons.enums.OrderProcessStatus;
import com.yeepay.ng.riskguard.commons.enums.StageType;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.commons.utils.OrderNoUtils;
import com.yeepay.ng.riskguard.engine.facade.namelist.dto.NameListDTO;
import com.yeepay.ng.riskguard.engine.facade.namelist.dto.NameListDetailDTO;
import com.yeepay.ng.riskguard.engine.facade.rule.dto.data.RuleExecuteItemDTO;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideRuleIdDTO;
import com.yeepay.ng.riskguard.gateway.facade.event.ConfirmDecideFinishEvent;
import com.yeepay.ng.riskguard.streaming.domain.entity.*;
import com.yeepay.ng.riskguard.streaming.domain.service.OrderService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/12 15:10
 */
@Slf4j
@Component
public class ConfirmHandleStrategy implements EventTypeHandleStrategy {
    private static final JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    @Setter(onMethod_ = @Autowired)
    private OrderService orderService;

    @Override
    @Transactional
    public void handle(String message) {
        ConfirmDecideFinishEvent confirmEvent = null;
        try {
            confirmEvent = JSON_PARSER.parse(message, ConfirmDecideFinishEvent.class);
        } catch (Exception e) {
            log.error("JSON解析失败，message：{}", message);
            return;
        }
        if (null == confirmEvent) {
            log.info("confirmEvent is null, message:{}", message);
            return;
        }
        log.info("receive confirm event, requestNo:{}, bizCode:{}, actualStatus:{}", confirmEvent.getRequestNo(), confirmEvent.getBizCode(), confirmEvent.getActualStatus());
        OrderQueryReqest queryReqest = new OrderQueryReqest();
        queryReqest.setBizCode(confirmEvent.getBizCode());
        queryReqest.setRequestNo(confirmEvent.getRequestNo());
        queryReqest.setScenarioCode(confirmEvent.getScenarioCode());
        queryReqest.setStage(StageType.INQUIRY.name());
        String actualStatus = confirmEvent.getActualStatus();
        Order order = orderService.find(queryReqest);
        if (null != order) {
            log.info("order is exist, requestNo:{}, bizCode:{}", confirmEvent.getRequestNo(), confirmEvent.getBizCode());
            OrderUpdateReqest updateReqest = new OrderUpdateReqest();
            if (StringUtils.isBlank(actualStatus)) {
                actualStatus = OrderActualStatus.PROCESS.name();
            }
            updateReqest.setActualStatus(OrderActualStatus.valueOf(actualStatus));
            updateReqest.setScenarioCode(confirmEvent.getScenarioCode());
            updateReqest.setBizCode(confirmEvent.getBizCode());
            updateReqest.setRequestNo(confirmEvent.getRequestNo());
            updateReqest.setResponseResult(confirmEvent.getResponseResult());
            orderService.updateStatus(updateReqest);
        }

        // 创建订单
        orderService.create(buildOrder(confirmEvent), true);
    }

    private Order buildOrder(ConfirmDecideFinishEvent confirmEvent) {
        Order order = new Order();

        order.setResponseResult(confirmEvent.getResponseResult());
        order.setOrderNo(confirmEvent.getOrderNo());
        order.setBatchNo(confirmEvent.getBatchNo());
        order.setEnvironment(confirmEvent.getEnvironment());
        order.setRequestAt(OrderNoUtils.parseRequestAt(confirmEvent.getOrderNo().substring(0, 14)));
        order.setRequestNo(confirmEvent.getRequestNo());
        order.setBizCode(confirmEvent.getBizCode());
        order.setScenarioCode(confirmEvent.getScenarioCode());
        order.setRequestParams(confirmEvent.getParams());
        order.setDecideType(confirmEvent.getDecideType());
        order.setActualStatus(EnumUtils.getEnum(OrderActualStatus.class, confirmEvent.getActualStatus()));
        order.setProcessStatus(EnumUtils.getEnum(OrderProcessStatus.class, confirmEvent.getProcessStatus()));
        order.setErrorMsg(confirmEvent.getErrorMsg());
        order.setPolicyCode(confirmEvent.getPolicyCode());
        order.setPolicyVersion(confirmEvent.getPolicyVersion());
        order.setStage(StageType.CONFIRM.name());
        order.setNotifyStatus(confirmEvent.getNotifyStatus());
        order.setNotifyUrl(confirmEvent.getNotifyUrl());

        DecideLog decideLog = new DecideLog();
        if (null != confirmEvent.getDecideLog() && MapUtils.isNotEmpty(confirmEvent.getDecideLog().getActionResult())) {
            Map<RuleExecuteItem, Object> actionResult = new HashMap<>();
            for (Map.Entry<DecideRuleIdDTO, Object> entry : confirmEvent.getDecideLog().getActionResult().entrySet()) {
                RuleExecuteItem ruleExecuteItem = RuleExecuteItem.builder()
                        .scope(entry.getKey().getScope().name())
                        .ruleCode(entry.getKey().getRuleCode())
                        .ruleVersion(String.valueOf(entry.getKey().getRuleVersion()))
                        .build();
                actionResult.put(ruleExecuteItem, entry.getValue());
            }
            decideLog.setActionResult(actionResult);
        }
        decideLog.setIndicatorValues(confirmEvent.getDecideLog().getIndicatorValues());
        decideLog.setFailedRules(convertRules(confirmEvent.getDecideLog().getFailedRules()));
        decideLog.setHitRules(convertRules(confirmEvent.getDecideLog().getHitRules()));
        decideLog.setVariables(confirmEvent.getDecideLog().getVariables());
        decideLog.setHitNameList(convertNameLists(confirmEvent.getDecideLog().getHitNameList()));
        decideLog.setFunctionVariables(confirmEvent.getDecideLog().getFunctionVariables());
        decideLog.setHitNameListRules(convertNameListRules(confirmEvent.getDecideLog().getHitNameListRules()));
        order.setDecideLog(decideLog);
        return order;
    }

    private List<RuleExecuteItem> convertNameListRules(List<RuleExecuteItemDTO> hitNameListRules) {
        if (CollectionUtils.isEmpty(hitNameListRules)) {
            return Collections.emptyList();
        }
        List<RuleExecuteItem> result = new ArrayList<>();
        for (RuleExecuteItemDTO nameListRuleDTO : hitNameListRules) {
            result.add(RuleExecuteItem.builder()
                    .ruleCode(nameListRuleDTO.getRuleCode())
                    .ruleVersion(String.valueOf(nameListRuleDTO.getRuleVersion()))
                    .decideType(nameListRuleDTO.getDecideType())
                    .build());
        }
        return result;
    }

    private List<NameList> convertNameLists(List<NameListDTO> nameLists) {
        if (CollectionUtils.isEmpty(nameLists)) {
            return Collections.emptyList();
        }
        List<NameList> result = new ArrayList<>();
        for (NameListDTO nameListDTO : nameLists) {
            result.add(NameList.builder()
                    .code(nameListDTO.getCode())
                    .type(nameListDTO.getType())
                    .effectiveScope(CollectionUtils.isEmpty(nameListDTO.getEffectiveScope())?null:nameListDTO.getEffectiveScope().get(0))
                    .decideType(nameListDTO.getDecideType())
                    .details(convertNameListDetails(nameListDTO.getDetails()))
                    .variableCode(nameListDTO.getVariableCode())
                    .categoryList(nameListDTO.getCategoryList())
                    .build());
        }
        return result;
    }

    private List<NameListDetail> convertNameListDetails(List<NameListDetailDTO> details) {
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
        List<NameListDetail> result = new ArrayList<>();
        for (NameListDetailDTO detail : details) {
            result.add(NameListDetail.builder()
                    .id(detail.getId())
                    .version(detail.getVersion())
                    .categoryValues(detail.getCategoryValues())
                    .build());
        }
        return result;
    }

    private List<RuleExecuteItem> convertRules(List<RuleExecuteItemDTO> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return Collections.EMPTY_LIST;
        }
        List<RuleExecuteItem> result = new ArrayList<>();
        for (RuleExecuteItemDTO rule : rules) {
            result.add(RuleExecuteItem.builder()
                    .ruleCode(rule.getRuleCode())
                    .ruleVersion(String.valueOf(rule.getRuleVersion()))
                    .matchFailed(rule.getMatchFailed())
                    .enabled(rule.getEnabled())
                    .decideType(rule.getDecideType())
                    .build());

        }
        return result;
    }

    @Override
    public String name() {
        return "confirm";
    }

    @PostConstruct
    public void register() {
        EventTypeHandleFactory.register(this);
    }
}
