<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yeepay.ng.riskguard</groupId>
        <artifactId>riskguard-streaming-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>riskguard-streaming-infrastructure</artifactId>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- redis start -->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <!-- redis end -->

        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-streaming-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-rules-commons</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-rules-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-gateway-facade</artifactId>
        </dependency>

        <!-- mybatis start -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.boot.components</groupId>
            <artifactId>yeepay-boot-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ibm.db2</groupId>
            <artifactId>jcc</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!-- mybatis end -->

        <!--log4j2 begin-->
        <dependency>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>infra-log4j2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-log4j2-pattern</artifactId>
        </dependency>
        <!--log4j2 end-->

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-utils-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-config</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
    </dependencies>
</project>
