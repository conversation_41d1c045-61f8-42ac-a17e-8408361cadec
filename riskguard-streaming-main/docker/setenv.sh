ENV_TEMP="prod"
if [ "-$YP_DATA_CENTER" = "-QA" ]
then
    ENV_TEMP="prod,qa"
else
    ENV_TEMP="prod,production"
fi

JAVA_ARGS="$JAVA_ARGS --spring.profiles.active=$ENV_TEMP"

JAVA_OPTS="$JAVA_OPTS -Dserver.http.port=8080"
JAVA_OPTS="$JAVA_OPTS -Dappname=riskguard-streaming"

# JAVA_ARGS根据自己应用实际情况来
#if [ "-$YP_DATA_CENTER" = "-QA" ]
#then
#    JAVA_ARGS="$JAVA_ARGS --spring.profiles.active=prod,qa"
#else
#    JAVA_ARGS="$JAVA_ARGS --spring.profiles.active=prod,production"
#fi
# oom后的动作
JAVA_OPTS="$JAVA_OPTS -XX:ErrorFile=/apps/gc_error_crash_%p.log"
JAVA_OPTS="$JAVA_OPTS -XX:HeapDumpPath=/apps/"

JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC"
JAVA_OPTS="$JAVA_OPTS -XX:MaxGCPauseMillis=200"
JAVA_OPTS="$JAVA_OPTS -Xss228k"
JAVA_OPTS="$JAVA_OPTS -server"

# 8C8G
JAVA_OPTS="$JAVA_OPTS -XX:ParallelGCThreads=8"
JAVA_OPTS="$JAVA_OPTS -XX:ConcGCThreads=2"
JAVA_OPTS="$JAVA_OPTS -Xms4500m -Xmx4500m"
JAVA_OPTS="$JAVA_OPTS -XX:MaxDirectMemorySize=1024M"

JAVA_OPTS="$JAVA_OPTS -XX:+UseStringDeduplication"
JAVA_OPTS="$JAVA_OPTS -XX:+UseLargePages"
JAVA_OPTS="$JAVA_OPTS -XX:-OmitStackTraceInFastThrow"
JAVA_OPTS="$JAVA_OPTS -XX:+UseFastAccessorMethods"
JAVA_OPTS="$JAVA_OPTS -Djava.security.egd=file:/dev/./urandom"
JAVA_OPTS="$JAVA_OPTS -Xshare:off"

# 网络相关
JAVA_OPTS="$JAVA_OPTS -Djava.net.preferIPv4Stack=true"
JAVA_OPTS="$JAVA_OPTS -Dsun.net.http.retryPost=false"
JAVA_OPTS="$JAVA_OPTS -Dsun.net.inetaddr.ttl=-1"

# 组件或易宝特有
JAVA_OPTS="$JAVA_OPTS -Drocketmq.client.logUseSlf4j=true"
JAVA_OPTS="$JAVA_OPTS -Drocketmq.client.logLevel=WARN"
JAVA_OPTS="$JAVA_OPTS -Dlog4j2.formatMsgNoLookups=true"
JAVA_OPTS="$JAVA_OPTS -Ddubbo.application.environment=$DUBBO_APPLICATION_ENVIRONMENT"
JAVA_OPTS="$JAVA_OPTS -Djute.maxbuffer=41943040"
JAVA_OPTS="$JAVA_OPTS -Djava.io.tmpdir=/apps/data/java-tmpdir"
JAVA_OPTS="$JAVA_OPTS -Ddbconfigpath=/apps/commoncfg/runtimecfg/"
# tomcat
#JAVA_OPTS="$JAVA_OPTS -Ddbconfigpath=/apps/tomcat/commoncfg/runtimecfg/"


# JMX
pubip="`/sbin/ifconfig  | grep 'inet addr:'| grep -v '127.0.0.1' | cut -d: -f2 | awk '{ print $1}'`"
if [ "-$pubip" == "-" ]
then
{
  pubip=""
}
fi
if [ "-$pubip" != "-" ]
then
{
  JAVA_OPTS="$JAVA_OPTS -Djava.rmi.server.hostname=$pubip"
}
fi

JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote"
JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.port=6969"
JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.ssl=false"
JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.authenticate=false"

# QA打开远程调试
if [ "-$YP_DATA_CENTER" = "-QA" ]
then
    JAVA_OPTS="$JAVA_OPTS -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=50055"
fi

java $JAVA_OPTS -Dfile.encoding='UTF-8' -XX:OnOutOfMemoryError="kill -9 %p" -Xbootclasspath/p:/apps/commoncfg/ -jar /apps/app.jar $JAVA_ARGS