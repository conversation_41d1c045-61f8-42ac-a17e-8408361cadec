<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yeepay.ng.riskguard</groupId>
        <artifactId>riskguard-streaming-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>riskguard-streaming-main</artifactId>
    <packaging>jar</packaging>

    <name>riskguard-streaming-main</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yeepay.boot.components</groupId>
            <artifactId>yeepay-boot-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-streaming-app</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-rules-commons</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-utils-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.starter</groupId>
            <artifactId>yeepay-soa-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-streaming-infrastructure</artifactId>
        </dependency>

        <!-- test start -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- test end -->
    </dependencies>
    <build>
        <finalName>riskguard-streaming</finalName>
        <plugins>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.4.4</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <skip>false</skip>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <layout>ZIP</layout>
                    <mainClass>com.yeepay.ng.riskguard.streaming.StreamingWebApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
