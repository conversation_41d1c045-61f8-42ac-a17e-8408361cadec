/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.utils.common.InitializeUtils;
import com.yeepay.springframework.boot.annotation.EnableSoa;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.context.annotation.PropertySource;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since  2024/12/9 16:08
 */
@SpringBootApplication(scanBasePackages = {"com.yeepay.ng.riskguard.streaming.*"},
        exclude = {
                DataSourceAutoConfiguration.class,
                TransactionAutoConfiguration.class})
@PropertySource(value = "classpath:runtimecfg/kafka.properties")
@PropertySource(value = {"classpath:runtimecfg/spring-redis-conf.properties"})
@EnableSoa
public class StreamingWebApplication {

    public static void main(String[] args) {
        System.setProperty("appname", "riskguard-streaming");
        InitializeUtils.initComponents();
        SpringApplication.run(StreamingWebApplication.class, args);
    }
}
