package com.yeepay.ng.riskguard.streaming.listener;

import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.gateway.facade.event.ConfirmDecideFinishEvent;
import com.yeepay.ng.riskguard.gateway.facade.event.DecideFinishEvent;
import com.yeepay.ng.riskguard.streaming.app.service.SourceEventService;
import io.netty.util.Timeout;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeoutException;

/**
 * title:
 * description: 指标累计
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/3/7 11:52 上午
 */
@Component
@Slf4j
public class SourceEventListener {

    private static final String EVENT_TYPE_CONFIRM = "confirm";
    private static final String EVENT_TYPE_DECIDE = "decide";
    private static final String EVENT_TYPE_UPDATE = "update";


    @Setter(onMethod_ = @Autowired)
    private SourceEventService sourceEventService;

    private static final JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();
    @KafkaListener(
            containerFactory = "riskguardEventContainerFactory",
            topics = "${riskguard.decide.finish.topic}",
            concurrency = "${consumer.currency:5}",
            groupId = "riskguard_event_source_consumer")
    public void onMessage(@Payload String message,
                          @Header("event-type") String eventType,
                          Acknowledgment acknowledgment) {
        try {
            processMessage(message, eventType);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            // 是否需要
            handleError(e,acknowledgment);
            log.error("Failed to process message. Event type: {}, Message: {}", eventType, message, e);
        }
    }

    private void processMessage(String message, String eventType) {
        switch (eventType) {
            case EVENT_TYPE_CONFIRM:
                handleConfirmEvent(message);
                break;
            case EVENT_TYPE_DECIDE:
                handleDecideEvent(message);
                break;
            case EVENT_TYPE_UPDATE:
                log.info("update event received is not hand");
                break;
            default:
                throw new RuntimeException("Unsupported event type: " + eventType);
        }
    }

    private void handleConfirmEvent(String message) {
        ConfirmDecideFinishEvent event = parseEvent(message, ConfirmDecideFinishEvent.class);
        log.info("Confirm event received: {}", event);
        sourceEventService.onConfirmEvent(event);
    }

    private void handleDecideEvent(String message) {
        DecideFinishEvent event = parseEvent(message, DecideFinishEvent.class);
        log.info("Decide finish event received: {}", event);
        sourceEventService.onDecideFinishEvent(event);
    }

    private <T> T parseEvent(String message, Class<T> eventClass) {
        T event = JSON_PARSER.parse(message, eventClass);
        if (event == null) {
            throw new RuntimeException("Failed to parse event from message: " + message);
        }
        return event;
    }

    private void handleError(Exception e, Acknowledgment acknowledgment) {
        if (isRetryableException(e)) {
            // 可重试的异常，抛出异常触发重试机制
            throw new RuntimeException("Retryable error occurred", e);
        } else {
            // 不可重试的异常，记录日志并确认消息
            log.error("Non-retryable error occurred, acknowledging message", e);
            acknowledgment.acknowledge();
        }
    }

    private boolean isRetryableException(Exception e) {
        // 定义哪些异常需要重试
        return !(e instanceof MyBatisSystemException ||
                e instanceof RuntimeException);
    }
}
