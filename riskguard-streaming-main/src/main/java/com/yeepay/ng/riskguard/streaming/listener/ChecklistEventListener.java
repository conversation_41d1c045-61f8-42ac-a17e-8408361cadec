package com.yeepay.ng.riskguard.streaming.listener;

import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.gateway.facade.event.ConfirmDecideFinishEvent;
import com.yeepay.ng.riskguard.streaming.app.executor.ChecklistExecutor;
import com.yeepay.ng.riskguard.streaming.app.executor.RiskguardExecutor;
import com.yeepay.ng.riskguard.streaming.domain.model.ChecklistMessage;
import com.yeepay.ng.riskguard.streaming.infrastructure.factory.EventTypeHandleFactory;
import com.yeepay.ng.riskguard.streaming.infrastructure.factory.EventTypeHandleStrategy;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/22 11:19
 */
@Component
@Slf4j
public class ChecklistEventListener {

    @Setter(onMethod_ = @Autowired)
    private ChecklistExecutor checklistExecutor;

    private static final JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    @KafkaListener(
            containerFactory = "riskguardChecklistEventContainerFactory",
            topics = "${riskguard.checklist.topic}",
            concurrency = "${checklist.consumer.currency:1}",
            groupId = "riskguard_checklist_event_order_consumer")
    public void onMessage(@Payload String message,
                          Acknowledgment acknowledgment) {
        log.info("riskguard_checklist_event message. message:{}", message);
        ChecklistMessage checklistMessage = JSON_PARSER.parse(message, ChecklistMessage.class);
        checklistExecutor.report(checklistMessage);
        acknowledgment.acknowledge();
    }
}
