package com.yeepay.ng.riskguard.streaming.listener;

import com.yeepay.ng.riskguard.commons.enums.EnvironmentType;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.gateway.facade.dto.confirm.ConfirmCommand;
import com.yeepay.ng.riskguard.gateway.facade.dto.confirm.ConfirmExtCommand;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideCommand;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideExtCommand;
import com.yeepay.ng.riskguard.gateway.facade.event.ConfirmDecideFinishEvent;
import com.yeepay.ng.riskguard.gateway.facade.event.DecideFinishEvent;
import com.yeepay.ng.riskguard.streaming.app.executor.RiskguardExecutor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/3/13 17:26
 */
@Component
@Slf4j
public class AccompanyEventListener {

    @Setter(onMethod_ = @Autowired)
    private RiskguardExecutor riskguardExecutor;


    private static final JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    @KafkaListener(
            containerFactory = "riskguardAccompanyEventContainerFactory",
            topics = "${riskguard.decide.finish.topic}",
            concurrency = "${accompany.consumer.currency:1}",
            groupId = "riskguard_event_accompany_new_consumer")
    public void onMessage(@Payload String message,
                          @Header("event-type") String eventType,
                          Acknowledgment acknowledgment) {
        try {
            if ("confirm".equals(eventType)) {
                log.debug("confirmDecideFinishEvent,message:{}", message);
                ConfirmDecideFinishEvent confirmDecideFinishEvent = JSON_PARSER.parse(message, ConfirmDecideFinishEvent.class);
                if (null == confirmDecideFinishEvent) {
                    log.error("confirmDecideFinishEvent is null");
                    return;
                }
                // 本身就是陪跑的请求直接跳过，否则就死循环了。。
                if (EnvironmentType.ACCOMPANY.name().equals(confirmDecideFinishEvent.getEnvironment())) {
                    log.info("confirmDecideFinishEvent is accompany");
                    return;
                }
                log.info("confirmDecideFinishEvent received:{}", confirmDecideFinishEvent);
                riskguardExecutor.confirm(buildConfirmCommand(confirmDecideFinishEvent), buildConfirmExtCommand(confirmDecideFinishEvent));
            } else {
                DecideFinishEvent decideFinishEvent = JSON_PARSER.parse(message, DecideFinishEvent.class);
                if (decideFinishEvent == null) {
                    log.error("decideFinishEvent is null");
                    return;
                }

                // 本身就是陪跑的请求直接跳过，否则就死循环了。。
                if (EnvironmentType.ACCOMPANY.name().equals(decideFinishEvent.getEnvironment())) {
                    log.info("decideFinishEvent is accompany");
                    return;
                }

                log.info("decideFinishEvent received:{}", decideFinishEvent);
                riskguardExecutor.decide(buildDecideCommand(decideFinishEvent), buildDecideExtCommand(decideFinishEvent));
            }

        } catch (Exception e) {
            log.error("error happened when handle event:{}", message, e);
        }
        acknowledgment.acknowledge();
    }

    private DecideCommand buildDecideCommand(DecideFinishEvent decideFinishEvent) {
        return DecideCommand.builder()
                .bizCode(decideFinishEvent.getBizCode())
                .scenarioCode(decideFinishEvent.getScenarioCode())
                .requestNo(decideFinishEvent.getRequestNo())
                .params(decideFinishEvent.getParams())
                .build();

    }

    private DecideExtCommand buildDecideExtCommand(DecideFinishEvent decideFinishEvent) {
        DecideExtCommand decideExtCommand = new DecideExtCommand();
        decideExtCommand.setEnvironment(EnvironmentType.ACCOMPANY.name());
        if (null != decideFinishEvent.getDecideLog()) {
            decideExtCommand.setIndicatorValues(decideFinishEvent.getDecideLog().getIndicatorValues());
        }
        decideExtCommand.setBatchNo(decideFinishEvent.getBatchNo());
        decideExtCommand.setRequestAt(decideFinishEvent.getRequestAt());
        return decideExtCommand;
    }


    private ConfirmCommand buildConfirmCommand(DecideFinishEvent decideFinishEvent) {
        ConfirmCommand confirmCommand = new ConfirmCommand();
        confirmCommand.setBizCode(decideFinishEvent.getBizCode())
                .setScenarioCode(decideFinishEvent.getScenarioCode())
                .setRequestNo(decideFinishEvent.getRequestNo())
                .setParams(decideFinishEvent.getParams());
        confirmCommand.setStatus(decideFinishEvent.getActualStatus());
        return confirmCommand;
    }

    private ConfirmExtCommand buildConfirmExtCommand(DecideFinishEvent decideFinishEvent) {
        ConfirmExtCommand confirmExtCommand = new ConfirmExtCommand();
        confirmExtCommand.setEnvironment(EnvironmentType.ACCOMPANY.name());
        if (null != decideFinishEvent.getDecideLog()) {
            confirmExtCommand.setIndicatorValues(decideFinishEvent.getDecideLog().getIndicatorValues());
        }
        confirmExtCommand.setBatchNo(decideFinishEvent.getBatchNo());
        confirmExtCommand.setRequestAt(decideFinishEvent.getRequestAt());
        return confirmExtCommand;
    }

}
