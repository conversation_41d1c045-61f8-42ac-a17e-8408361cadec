package com.yeepay.ng.riskguard.streaming.listener;

import com.yeepay.ng.riskguard.streaming.infrastructure.factory.EventTypeHandleFactory;
import com.yeepay.ng.riskguard.streaming.infrastructure.factory.EventTypeHandleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * title:
 * description: 订单持久化
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/3/7 11:52 上午
 */
@Component
@Slf4j
public class DatasourcePersistenceListener {
    @KafkaListener(
            containerFactory = "riskguardPersistenceEventContainerFactory",
            topics = "${riskguard.decide.finish.topic}",
            concurrency = "${persistence.consumer.currency:3}",
            groupId = "replace_riskguard_event_persistence_consumer")
    public void onMessage(@Payload String message,
                          @Header("event-type") String eventType,
                          Acknowledgment acknowledgment) {
        EventTypeHandleStrategy strategy = EventTypeHandleFactory.get(eventType);
        strategy.handle(message);
        acknowledgment.acknowledge();
    }
}
