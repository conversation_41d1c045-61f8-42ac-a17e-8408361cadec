/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.listener;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonParseException;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.ng.riskguard.commons.enums.EnvironmentType;
import com.yeepay.ng.riskguard.commons.enums.StageType;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.engine.facade.order.RiskGuardOrder;
import com.yeepay.ng.riskguard.engine.spel.ExecuteParamKeys;
import com.yeepay.ng.riskguard.engine.spel.ParamsMap;
import com.yeepay.ng.riskguard.engine.spel.SpelExecutor;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideLogDTO;
import com.yeepay.ng.riskguard.gateway.facade.event.ConfirmDecideFinishEvent;
import com.yeepay.ng.riskguard.gateway.facade.event.DecideFinishEvent;
import com.yeepay.ng.riskguard.streaming.app.service.impl.DecideSender;
import com.yeepay.ng.riskguard.streaming.domain.entity.Order;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderQueryReqest;
import com.yeepay.ng.riskguard.streaming.domain.entity.PolicyOrchestrationList;
import com.yeepay.ng.riskguard.streaming.domain.entity.PolicyOrchestrationRequest;
import com.yeepay.ng.riskguard.streaming.domain.model.RiskRuleExpression;
import com.yeepay.ng.riskguard.streaming.domain.service.OrderService;
import com.yeepay.ng.riskguard.streaming.domain.service.PolicyOrchestrationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * title:
 * description: 订单持久化
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/3/7 11:52 上午
 */
@Component
@Slf4j
public class OrchestrationEventListener {

    private static final JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();
    private static final String EVENT_TYPE_CONFIRM = "confirm";
    private static final String EVENT_TYPE_DECIDE = "decide";
    private static final String EVENT_TYPE_UPDATE = "update";
    private static final String SPEL_LANGUAGE = "SPEL";
    private static final String EXECUTE_METHOD_SYNC = "SYNC";
    private static final String EXECUTE_METHOD_ASYNC = "ASYNC";

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private final DecideSender decideSender;
    private final PolicyOrchestrationService policyOrchestrationService;
    private final SpelExecutor spelExecutor;
    private final OrderService orderService;

    @Autowired
    public OrchestrationEventListener(
            DecideSender decideSender,
            PolicyOrchestrationService policyOrchestrationService,
            SpelExecutor spelExecutor, OrderService orderService) {
        this.decideSender = decideSender;
        this.policyOrchestrationService = policyOrchestrationService;
        this.spelExecutor = spelExecutor;
        this.orderService = orderService;
    }

    @KafkaListener(
            containerFactory = "riskguardOrchestrationEventContainerFactory",
            topics = "${riskguard.decide.finish.topic}",
            concurrency = "${orchestration.consumer.currency:5}",
            groupId = "riskguard_event_Orchestration_consumer")
    public void onMessage(@Payload String message,
                          @Header("event-type") String eventType,
                          Acknowledgment acknowledgment) {
        try {
            processMessage(message, eventType);
            acknowledgment.acknowledge();
        } catch (Exception e) {
            log.error("Error processing message: {}, event type: {}", message, eventType, e);
            // 根据异常类型决定是否确认消息,目前最大重试五次
            if (shouldAcknowledgeException(e)) {
                log.warn("Acknowledging message despite error due to exception type: {}", e.getClass().getName());
                acknowledgment.acknowledge();
            } else {
                log.warn("Not acknowledging message due to recoverable error: {}", e.getClass().getName());
            }
        }
    }

    /**
     * @param e 捕获的异常
     * @return 如果应该确认消息返回true，否则返回false
     */
    private boolean shouldAcknowledgeException(Exception e) {
        // 参数错误
        return e instanceof IllegalArgumentException ||
                // 不支持的操作
                e instanceof UnsupportedOperationException ||
                // JSON解析错误
                e instanceof JsonParseException ||
                // 空指针异常
                e instanceof NullPointerException ||
                (e.getCause() != null && shouldAcknowledgeException((Exception) e.getCause()));

    }

    private void processMessage(String message, String eventType) {
        log.info("接收到风控订单异步编排OrchestrationEventListener: {}", message);
        switch (eventType) {
            case EVENT_TYPE_CONFIRM:
                handleConfirmEvent(message);
                break;
            case EVENT_TYPE_DECIDE:
                handleDecideEvent(message);
                break;
            case EVENT_TYPE_UPDATE:
                handleUpdateEvent(message);
                break;
            default:
                log.warn("Unsupported event type: {}", eventType);
                throw new UnsupportedOperationException("Unsupported event type: " + eventType);
        }
    }

    private void handleUpdateEvent(String message) {
        RiskGuardOrder updateOrder = parseEvent(message, RiskGuardOrder.class);
        log.info("Update event received: {}", updateOrder);

        // 本身就是陪跑的请求直接跳过，否则就死循环了。。
        if (EnvironmentType.ACCOMPANY.name().equals(updateOrder.getEnvironment())) {
            log.info("Skipping update event processing for accompany environment");
            return;
        }

        // 确保关键字段不为空
        if (!isValidUpdateEvent(updateOrder)) {
            log.warn("Invalid update event: missing required fields");
            return;
        }

        List<PolicyOrchestrationList> handlers = findOrchestrationHandlers(updateOrder.getPolicyCode(), updateOrder.getPolicyVersion());

        if (handlers.isEmpty()) {
            log.info("No policy orchestration handlers found for policy: {}", updateOrder.getPolicyCode());
            return;
        }

        // 处理每个匹配的编排处理器
        handlers.forEach(handler -> processUpdateHandler(handler, updateOrder));
    }

    private boolean isValidUpdateEvent(RiskGuardOrder updateOrder) {
        return StringUtils.isNotBlank(updateOrder.getPolicyCode()) &&
                updateOrder.getPolicyVersion() != null &&
                StringUtils.isNotBlank(updateOrder.getRequestNo()) &&
                StringUtils.isNotBlank(updateOrder.getBizCode()) &&
                StringUtils.isNotBlank(updateOrder.getScenarioCode());
    }

    private void handleConfirmEvent(String message) {
        ConfirmDecideFinishEvent confirmEvent = parseEvent(message, ConfirmDecideFinishEvent.class);
        // 本身就是陪跑的请求直接跳过，否则就死循环了。。
        if (EnvironmentType.ACCOMPANY.name().equals(confirmEvent.getEnvironment())) {
            log.info("OrchestrationEventListener handleConfirmEvent is accompany");
            return;
        }
        log.info("Confirm event received: {}", message);
        if (isValidEvent(confirmEvent)) {
            processConfirmDecideEvent(confirmEvent);
        }
    }

    private void handleDecideEvent(String message) {
        DecideFinishEvent decideEvent = parseEvent(message, DecideFinishEvent.class);
        if (EnvironmentType.ACCOMPANY.name().equals(decideEvent.getEnvironment())) {
            log.info("OrchestrationEventListener handleDecideEvent is accompany");
            return;
        }
        log.info("Decide event received: {}", decideEvent);
        if (isValidEvent(decideEvent)) {
            processDecideEvent(decideEvent);
        }
    }

    private <T> T parseEvent(String message, Class<T> eventClass) {
        T event = JSON_PARSER.parse(message, eventClass);
        if (event == null) {
            log.error("Failed to parse event from message: {}", message);
            throw new IllegalArgumentException("Failed to parse event from message: " + message);
        }
        return event;
    }

    private <T> boolean isValidEvent(T event) {
        if (event instanceof DecideFinishEvent) {
            DecideFinishEvent decideEvent = (DecideFinishEvent) event;
            return StringUtils.isNotBlank(decideEvent.getPolicyCode()) && decideEvent.getPolicyVersion() != null;
        } else if (event instanceof ConfirmDecideFinishEvent) {
            ConfirmDecideFinishEvent confirmEvent = (ConfirmDecideFinishEvent) event;
            return StringUtils.isNotBlank(confirmEvent.getPolicyCode()) && confirmEvent.getPolicyVersion() != null;
        }
        return false;
    }

    private void processConfirmDecideEvent(ConfirmDecideFinishEvent confirmEvent) {
        List<PolicyOrchestrationList> handlers = findOrchestrationHandlers(confirmEvent.getPolicyCode(), confirmEvent.getPolicyVersion());

        if (handlers.isEmpty()) {
            log.info("No policy orchestration handlers found for policy: {}", confirmEvent.getPolicyCode());
            return;
        }

        handlers.forEach(handler -> processConfirmHandler(handler, confirmEvent));
    }

    private void processDecideEvent(DecideFinishEvent event) {
        List<PolicyOrchestrationList> handlers = findOrchestrationHandlers(event.getPolicyCode(), event.getPolicyVersion());

        if (handlers.isEmpty()) {
            log.info("No policy orchestration handlers found for policy: {}", event.getPolicyCode());
            return;
        }

        handlers.forEach(handler -> processHandler(handler, event));
    }

    private List<PolicyOrchestrationList> findOrchestrationHandlers(String policyCode, Long policyVersion) {
        PolicyOrchestrationRequest request = PolicyOrchestrationRequest.builder()
                .policyCode(policyCode)
                .policyVersion(policyVersion)
                .build();

        try {
            return policyOrchestrationService.find(request);
        } catch (Exception e) {
            log.error("Error finding orchestration handlers for policy: {}, version: {}", policyCode, policyVersion, e);
            throw new RuntimeException("Failed to find orchestration handlers", e);
        }
    }

    private void processConfirmHandler(PolicyOrchestrationList handler, ConfirmDecideFinishEvent confirmEvent) {
        Map<String, Object> engineParams = prepareEngineParams(confirmEvent);
        List<String> spelExpressions = getSpelExpressions(handler);

        if (areExpressionsMatched(spelExpressions, engineParams)) {
            executeConfirmHandler(handler, confirmEvent);
        }
    }

    private void processUpdateHandler(PolicyOrchestrationList handler, RiskGuardOrder updateOrder) {
        log.info("Processing update handler for request: {}, policy: {}", updateOrder.getRequestNo(), handler.getPolicyCode());

        // 检查订单是否已经存在于目标业务场景
        OrderQueryReqest targetRequest = new OrderQueryReqest();
        targetRequest.setBizCode(handler.getBizCode());
        targetRequest.setRequestNo(updateOrder.getRequestNo());
        targetRequest.setScenarioCode(handler.getScenarioCode());
        targetRequest.setStage(handler.getStage());

        Order existingTargetOrder = orderService.find(targetRequest);
        if (existingTargetOrder != null) {
            log.info("Order already exists in target business scenario: requestNo={}, bizCode={}, scenarioCode={}",
                    updateOrder.getRequestNo(), handler.getBizCode(), handler.getScenarioCode());
            return;
        }

        // 检查SPEL表达式是否匹配
        Map<String, Object> engineParams = prepareUpdateParams(updateOrder);
        List<String> spelExpressions = getSpelExpressions(handler);

        if (!areExpressionsMatched(spelExpressions, engineParams)) {
            log.info("SPEL expressions did not match for update request: {}", updateOrder.getRequestNo());
            return;
        }

        // 查询源订单信息
        OrderQueryReqest sourceRequest = new OrderQueryReqest();
        sourceRequest.setBizCode(updateOrder.getBizCode());
        sourceRequest.setScenarioCode(updateOrder.getScenarioCode());
        sourceRequest.setRequestNo(updateOrder.getRequestNo());
        sourceRequest.setStage(updateOrder.getStage());
        sourceRequest.setEnvironment(updateOrder.getEnvironment());

        Order existingSourceOrder = orderService.find(sourceRequest);
        if (existingSourceOrder == null) {
            log.error("Source order not found: bizCode={}, scenarioCode={}, requestNo={}, stage={}",
                    updateOrder.getBizCode(), updateOrder.getScenarioCode(),
                    updateOrder.getRequestNo(), updateOrder.getStage());
            return;
        }

        // 执行更新处理
        executeUpdateHandler(handler, existingSourceOrder, updateOrder);
    }

    private void executeUpdateHandler(PolicyOrchestrationList handler, Order existingOrder, RiskGuardOrder updateOrder) {
        log.info("Executing update handler for order: {}, with method: {}", existingOrder.getOrderNo(), handler.getExecuteMethod());

        try {
            switch (handler.getExecuteMethod()) {
                case EXECUTE_METHOD_SYNC:
                    log.info("Executing sync update handler for order: {}", existingOrder.getOrderNo());
                    executeUpdateSyncHandler(handler, existingOrder, updateOrder);
                    break;

                case EXECUTE_METHOD_ASYNC:
                    // 异步处理方式 - 发送消息
                    log.info("Executing async update handler for order: {}", existingOrder.getOrderNo());
                    executeUpdateAsyncHandler(handler, existingOrder, updateOrder);
                    break;

                default:
                    log.warn("Unsupported execute method: {} for order: {}",
                            handler.getExecuteMethod(), existingOrder.getOrderNo());
                    throw new UnsupportedOperationException(
                            "Unsupported execute method: " + handler.getExecuteMethod());
            }

            log.info("Successfully executed update handler for order: {}", existingOrder.getOrderNo());

        } catch (Exception e) {
            log.error("Error executing update handler for order: {}, handler: {}",
                    existingOrder.getOrderNo(), handler.getPolicyCode(), e);
            throw new RuntimeException("Failed to execute update handler", e);
        }
    }

    private void executeUpdateSyncHandler(PolicyOrchestrationList handler, Order existingOrder, RiskGuardOrder updateOrder) {
        // 实现同步处理逻辑，直接调用接口，本期没实现
        log.info("Executing sync handler for event: {}", updateOrder);
    }

    private void executeUpdateAsyncHandler(PolicyOrchestrationList handler, Order existingOrder, RiskGuardOrder updateOrder) {
        log.info("Executing async update handler for order: {}, stage: {}",
                existingOrder.getOrderNo(), updateOrder.getStage());

        if (StageType.INQUIRY.name().equals(updateOrder.getStage())) {
            // 处理问询阶段的更新事件
            log.info("Processing inquiry stage update for order: {}", existingOrder.getOrderNo());
            DecideFinishEvent decideFishEvent = buildDecideFinishUpdateEvent(updateOrder, existingOrder, handler);
            decideSender.sendDecideEvent(decideFishEvent);
            log.info("Inquiry update event sent for order: {}", existingOrder.getOrderNo());

        } else if (StageType.CONFIRM.name().equals(updateOrder.getStage())) {
            // 处理确认阶段的更新事件
            log.info("Processing confirm stage update for order: {}", existingOrder.getOrderNo());
            ConfirmDecideFinishEvent command = buildConfirmDecideUpdateFinishCommand(updateOrder, existingOrder, handler);
            decideSender.sendConfirmEvent(command);
            log.info("Confirm update event sent for order: {}", existingOrder.getOrderNo());

        } else {
            // 不支持的阶段类型
            log.warn("Unsupported stage type for update: {} on order: {}",
                    updateOrder.getStage(), existingOrder.getOrderNo());
        }
    }

    private DecideFinishEvent buildDecideFinishUpdateEvent(RiskGuardOrder updateOrder, Order existingOrder, PolicyOrchestrationList handler) {
        Map<String, Object> mappedParams = mapParameters(updateOrder.getRequestParams(), handler.getParamMapping());
        // 更改特定属性
        return copyEventWithChangesUpdate(updateOrder, existingOrder, handler.getBizCode(), handler.getScenarioCode(), mappedParams);
    }

    private DecideFinishEvent copyEventWithChangesUpdate(RiskGuardOrder updateOrder, Order existingOrder, String bizCode, String scenarioCode, Map<String, Object> mappedParams) {
        // 构建新的决策日志实体
        DecideLogDTO newDecideLog = new DecideLogDTO();

        // 1. 指标返回结果 (indicatorValues)
        if (MapUtils.isNotEmpty(updateOrder.getIndicatorValues())) {
            try {
                newDecideLog.setIndicatorValues(updateOrder.getIndicatorValues());
                log.debug("更新指标返回结果成功");
            } catch (Exception e) {
                log.warn("设置指标返回结果异常: {}", e.getMessage());
            }
        }

        // 2. 命中的名单 (hitNameList)
        if (CollectionUtils.isNotEmpty(updateOrder.getHitNameList())) {
            try {
                newDecideLog.setHitNameList(updateOrder.getHitNameList());
                log.debug("更新命中名单成功");
            } catch (Exception e) {
                log.warn("设置命中名单异常: {}", e.getMessage());
            }
        }

        // 3. 系统变量 (variables)
        if (MapUtils.isNotEmpty(updateOrder.getVariables())) {
            try {
                newDecideLog.setVariables(updateOrder.getVariables());
                log.debug("更新系统变量成功");
            } catch (Exception e) {
                log.warn("设置系统变量异常: {}", e.getMessage());
            }
        }

        // 4. 函数变量 (functionVariables)
        if (MapUtils.isNotEmpty(updateOrder.getFunctionVariables())) {
            try {
                newDecideLog.setFunctionVariables(updateOrder.getFunctionVariables());
                log.debug("更新函数变量成功");
            } catch (Exception e) {
                log.warn("设置函数变量异常: {}", e.getMessage());
            }
        }

        // 命中的规则
        if (CollectionUtils.isNotEmpty(updateOrder.getHitRules())) {
            newDecideLog.setHitRules(updateOrder.getHitRules());
        }
        // 命中的名单规则
        if (CollectionUtils.isNotEmpty(updateOrder.getHitNameListRules())) {
            newDecideLog.setHitNameListRules(updateOrder.getHitNameListRules());
        }
        return DecideFinishEvent.builder()
                // 修改的属性
                .bizCode(bizCode)
                .scenarioCode(scenarioCode)
                .params(mappedParams)
                // 保留的属性
                .policyCode(updateOrder.getPolicyCode())
                .policyVersion(updateOrder.getPolicyVersion())
                .environment(updateOrder.getEnvironment())
                .orderNo(updateOrder.getOrderNo())
                .requestNo(updateOrder.getRequestNo())
                .batchNo(existingOrder.getBatchNo())
                .decideType(updateOrder.getDecideType())
                .requestAt(DateUtil.format(existingOrder.getRequestAt(), "yyyy-MM-dd HH:mm:ss"))
                .errorMsg(existingOrder.getErrorMsg())
                .notifyStatus(existingOrder.getNotifyStatus())
                .notifyUrl(updateOrder.getNotifyUrl())
                .actualStatus(Optional.ofNullable(existingOrder.getActualStatus()).map(Enum::name).orElse(null))
                .processStatus(Optional.ofNullable(existingOrder.getProcessStatus()).map(Enum::name).orElse(null))
                .persistent(true)
                .decideLog(newDecideLog)
                .build();
    }

    private ConfirmDecideFinishEvent buildConfirmDecideUpdateFinishCommand(RiskGuardOrder updateOrder, Order existingOrder, PolicyOrchestrationList handler) {
        Map<String, Object> mappedParams = mapParameters(updateOrder.getRequestParams(), handler.getParamMapping());
        // 构建新的决策日志实体
        DecideLogDTO newDecideLog = new DecideLogDTO();

        // 1. 指标返回结果 (indicatorValues)
        if (MapUtils.isNotEmpty(updateOrder.getIndicatorValues())) {
            try {
                newDecideLog.setIndicatorValues(updateOrder.getIndicatorValues());
                log.debug("更新指标返回结果成功");
            } catch (Exception e) {
                log.warn("设置指标返回结果异常: {}", e.getMessage());
            }
        }

        // 2. 命中的名单 (hitNameList)
        if (CollectionUtils.isNotEmpty(updateOrder.getHitNameList())) {
            try {
                newDecideLog.setHitNameList(updateOrder.getHitNameList());
                log.debug("更新命中名单成功");
            } catch (Exception e) {
                log.warn("设置命中名单异常: {}", e.getMessage());
            }
        }

        // 3. 系统变量 (variables)
        if (MapUtils.isNotEmpty(updateOrder.getVariables())) {
            try {
                newDecideLog.setVariables(updateOrder.getVariables());
                log.debug("更新系统变量成功");
            } catch (Exception e) {
                log.warn("设置系统变量异常: {}", e.getMessage());
            }
        }

        // 4. 函数变量 (functionVariables)
        if (MapUtils.isNotEmpty(updateOrder.getFunctionVariables())) {
            try {
                newDecideLog.setFunctionVariables(updateOrder.getFunctionVariables());
                log.debug("更新函数变量成功");
            } catch (Exception e) {
                log.warn("设置函数变量异常: {}", e.getMessage());
            }
        }

        // 命中的规则
        if (CollectionUtils.isNotEmpty(updateOrder.getHitRules())) {
            newDecideLog.setHitRules(updateOrder.getHitRules());
        }
        // 命中的名单规则
        if (CollectionUtils.isNotEmpty(updateOrder.getHitNameListRules())) {
            newDecideLog.setHitNameListRules(updateOrder.getHitNameListRules());
        }
        // 使用 SuperBuilder创建ConfirmDecideFinishEvent对象
        ConfirmDecideFinishEvent confirmCommand = ConfirmDecideFinishEvent.builder()
                // 设置要修改的三个属性
                .bizCode(handler.getBizCode())
                .scenarioCode(handler.getScenarioCode())
                .params(mappedParams)

                // 保留原始对象的属性
                .policyCode(updateOrder.getPolicyCode())
                .policyVersion(updateOrder.getPolicyVersion())
                .environment(updateOrder.getEnvironment())
                .orderNo(updateOrder.getOrderNo())
                .requestNo(updateOrder.getRequestNo())
                .batchNo(existingOrder.getBatchNo())
                .decideType(updateOrder.getDecideType())
                .requestAt(DateUtil.format(updateOrder.getRequestAt(), DATE_FORMAT))
                .errorMsg(existingOrder.getErrorMsg())
                .notifyStatus(existingOrder.getNotifyStatus())
                .notifyUrl(updateOrder.getNotifyUrl())
                .actualStatus(Optional.ofNullable(existingOrder.getActualStatus()).map(Enum::name).orElse(null))
                .processStatus(Optional.ofNullable(existingOrder.getProcessStatus()).map(Enum::name).orElse(null))
                .persistent(true)
                .decideLog(newDecideLog)
                .build();

        return confirmCommand;
    }

    private void processHandler(PolicyOrchestrationList handler, DecideFinishEvent event) {
        Map<String, Object> engineParams = prepareEngineParams(event);
        List<String> spelExpressions = getSpelExpressions(handler);

        if (areExpressionsMatched(spelExpressions, engineParams)) {
            executeHandler(handler, event);
        }
    }

    private boolean areExpressionsMatched(List<String> expressions, Map<String, Object> engineParams) {
        return expressions.isEmpty() || expressions.stream()
                .allMatch(expression -> spelExecutor.match(expression, engineParams));
    }

    private Map<String, Object> prepareEngineParams(DecideFinishEvent event) {
        Map<String, Object> engineParams = new HashMap<>();
        // 添加基本参数
        engineParams.put(ExecuteParamKeys.PARAMS, convertToParamsMap(event.getParams()));
        // 添加决策日志
        Optional.ofNullable(event.getDecideLog())
                .ifPresent(decideLog -> engineParams.put(ExecuteParamKeys.VARIABLE, decideLog.getVariables()));
        return engineParams;
    }

    private Map<String, Object> prepareUpdateParams(RiskGuardOrder updateOrder) {
        Map<String, Object> engineParams = new HashMap<>();
        // 添加基本参数
        engineParams.put(ExecuteParamKeys.PARAMS, convertToParamsMap(updateOrder.getRequestParams()));
        // 添加决策日志
        Optional.ofNullable(updateOrder.getVariables())
                .ifPresent(decideLog -> engineParams.put(ExecuteParamKeys.VARIABLE, updateOrder.getVariables()));
        return engineParams;
    }

    private List<String> getSpelExpressions(PolicyOrchestrationList handler) {
        return Optional.ofNullable(handler)
                .map(PolicyOrchestrationList::getExpressions)
                .orElse(Collections.emptyList())
                .stream()
                .filter(this::isSpelExpression)
                .map(RiskRuleExpression::getContent)
                .collect(Collectors.toList());
    }

    private boolean isSpelExpression(RiskRuleExpression expression) {
        return StringUtils.isNotBlank(expression.getLanguage())
                && SPEL_LANGUAGE.equals(expression.getLanguage());
    }

    private void executeConfirmHandler(PolicyOrchestrationList handler, ConfirmDecideFinishEvent confirmEvent) {
        try {
            switch (handler.getExecuteMethod()) {
                case EXECUTE_METHOD_SYNC:
                    executeSyncConfirmHandler(handler, confirmEvent);
                    break;
                case EXECUTE_METHOD_ASYNC:
                    executeConfirmAsyncHandler(handler, confirmEvent);
                    break;
                case EVENT_TYPE_UPDATE:
                    log.info("executeUpdateHandler method: {}", handler.getExecuteMethod());
                    break;
                default:
                    log.warn("Unsupported execute method: {}", handler.getExecuteMethod());
                    throw new UnsupportedOperationException(
                            "Unsupported execute method: " + handler.getExecuteMethod());
            }
        } catch (Exception e) {
            log.error("Error executing confirm handler: {}", handler, e);
            throw new RuntimeException("Failed to execute confirm handler", e);
        }
    }

    private void executeHandler(PolicyOrchestrationList handler, DecideFinishEvent event) {
        try {
            switch (handler.getExecuteMethod()) {
                case EXECUTE_METHOD_SYNC:
                    executeSyncHandler(handler, event);
                    break;
                case EXECUTE_METHOD_ASYNC:
                    executeAsyncHandler(handler, event);
                    break;
                default:
                    log.warn("Unsupported execute method: {}", handler.getExecuteMethod());
                    throw new UnsupportedOperationException(
                            "Unsupported execute method: " + handler.getExecuteMethod());
            }
        } catch (Exception e) {
            log.error("Error executing handler: {}", handler, e);
            throw new RuntimeException("Failed to execute handler", e);
        }
    }

    private void executeSyncConfirmHandler(PolicyOrchestrationList handler, ConfirmDecideFinishEvent event) {
        // 实现同步处理逻辑，直接调用接口,本期没实现,facade接口可以
        log.info("Executing sync confirm handler for event: {}", event);
    }

    private void executeSyncHandler(PolicyOrchestrationList handler, DecideFinishEvent event) {
        // 实现同步处理逻辑，直接调用接口，本期没实现
        log.info("Executing sync handler for event: {}", event);
    }

    private void executeConfirmAsyncHandler(PolicyOrchestrationList handler, ConfirmDecideFinishEvent confirmEvent) {
        ConfirmDecideFinishEvent command = buildConfirmDecideFinishCommand(confirmEvent, handler);
        decideSender.sendConfirmEvent(command);
        log.info("Async confirm command sent: {}", command);
    }

    private void executeAsyncHandler(PolicyOrchestrationList handler, DecideFinishEvent event) {
        DecideFinishEvent decideCommand = buildDecideFinishEvent(event, handler);
        decideSender.sendDecideEvent(decideCommand);
        log.info("Async decide command sent: {}", decideCommand);
    }

    private ConfirmDecideFinishEvent buildConfirmDecideFinishCommand(ConfirmDecideFinishEvent confirmEvent, PolicyOrchestrationList handler) {
        Map<String, Object> mappedParams = mapParameters(confirmEvent.getParams(), handler.getParamMapping());

        // 使用 SuperBuilder创建ConfirmDecideFinishEvent对象
        ConfirmDecideFinishEvent confirmCommand = ConfirmDecideFinishEvent.builder()
                // 设置要修改的三个属性
                .bizCode(handler.getBizCode())
                .scenarioCode(handler.getScenarioCode())
                .params(mappedParams)

                // 保留原始对象的属性
                .policyCode(confirmEvent.getPolicyCode())
                .policyVersion(confirmEvent.getPolicyVersion())
                .environment(confirmEvent.getEnvironment())
                .orderNo(confirmEvent.getOrderNo())
                .requestNo(confirmEvent.getRequestNo())
                .batchNo(confirmEvent.getBatchNo())
                .decideType(confirmEvent.getDecideType())
                .requestAt(confirmEvent.getRequestAt())
                .errorMsg(confirmEvent.getErrorMsg())
                .notifyStatus(confirmEvent.getNotifyStatus())
                .notifyUrl(confirmEvent.getNotifyUrl())
                .actualStatus(confirmEvent.getActualStatus())
                .processStatus(confirmEvent.getProcessStatus())
                .persistent(confirmEvent.getPersistent())
                .decideLog(confirmEvent.getDecideLog())
                .build();

        return confirmCommand;

    }

    private DecideFinishEvent buildDecideFinishEvent(DecideFinishEvent event, PolicyOrchestrationList handler) {
        Map<String, Object> mappedParams = mapParameters(event.getParams(), handler.getParamMapping());
        // 更改特定属性
        return copyEventWithChanges(event, handler.getBizCode(), handler.getScenarioCode(), mappedParams);
    }

    private DecideFinishEvent copyEventWithChanges(DecideFinishEvent event, String bizCode, String scenarioCode, Map<String, Object> mappedParams) {
        boolean originalPersistent = event.getPersistent();
        if (!originalPersistent) {
            log.info("Changed persistent from false to true for event: {}", event);
            originalPersistent = true;
        }
        return DecideFinishEvent.builder()
                // 修改的属性
                .bizCode(bizCode)
                .scenarioCode(scenarioCode)
                .params(mappedParams)
                // 保留的属性
                .policyCode(event.getPolicyCode())
                .policyVersion(event.getPolicyVersion())
                .environment(event.getEnvironment())
                .orderNo(event.getOrderNo())
                .requestNo(event.getRequestNo())
                .batchNo(event.getBatchNo())
                .decideType(event.getDecideType())
                .requestAt(event.getRequestAt())
                .errorMsg(event.getErrorMsg())
                .notifyStatus(event.getNotifyStatus())
                .notifyUrl(event.getNotifyUrl())
                .actualStatus(event.getActualStatus())
                .processStatus(event.getProcessStatus())
                .persistent(originalPersistent)
                .decideLog(event.getDecideLog())
                .build();
    }

    /**
     * 将源参数映射到目标参数结构，支持多重嵌套
     *
     * @param sourceParams 源参数，可能包含多重嵌套结构
     * @param paramMapping 参数映射关系，键是目标参数名，值是源参数路径
     * @return 映射后的参数
     */
    private Map<String, Object> mapParameters(Map<String, Object> sourceParams, Map<String, Object> paramMapping) {
        if (paramMapping == null || sourceParams == null) {
            return new HashMap<>();
        }

        Map<String, Object> result = new HashMap<>();

        for (Map.Entry<String, Object> entry : paramMapping.entrySet()) {
            String targetParam = entry.getKey();
            String sourcePath = entry.getValue().toString();

            // 获取编排之前的参数
            Object value = getValueByPath(sourceParams, sourcePath);

            if (value != null) {
                // 如果目标参数也包含点号，表示需要创建嵌套结构
                if (targetParam.contains(".")) {
                    setValueByPath(result, targetParam, value);
                } else {
                    result.put(targetParam, value);
                }
            }
        }

        return result;
    }

    /**
     * 根据路径获取嵌套Map中的值，支持任意深度嵌套
     *
     * @param sourceMap 源Map
     * @param path      路径，如"testData.subData.value"
     * @return 路径对应的值，如果路径不存在则返回null
     */
    private Object getValueByPath(Map<String, Object> sourceMap, String path) {
        if (sourceMap == null || path == null || path.isEmpty()) {
            return null;
        }

        // 分割路径
        String[] pathParts = path.split("\\.");

        // 当前对象
        Object current = sourceMap;

        // 遍历路径部分
        for (String part : pathParts) {
            // 如果当前对象不是Map或为null，返回null
            if (!(current instanceof Map)) {
                return null;
            }

            // 获取下一级对象
            @SuppressWarnings("unchecked")
            Map<String, Object> currentMap = (Map<String, Object>) current;
            current = currentMap.get(part);

            // 如果下一级对象为null，返回null
            if (current == null) {
                return null;
            }
        }

        // 返回最终对象
        return current;
    }

    /**
     * 根据路径设置嵌套Map中的值，
     *
     * @param targetMap
     * @param path      路径，如"testData.test.value"
     * @param value     要设置的值
     */
    private void setValueByPath(Map<String, Object> targetMap, String path, Object value) {
        if (targetMap == null || path == null || path.isEmpty()) {
            return;
        }

        // 分割路径
        String[] pathParts = path.split("\\.");

        // 当前Map
        Map<String, Object> currentMap = targetMap;

        // 遍历路径部分，除了最后一部分
        for (int i = 0; i < pathParts.length - 1; i++) {
            String part = pathParts[i];

            // 获取或创建下一级Map
            Object nextObj = currentMap.get(part);
            if (!(nextObj instanceof Map)) {
                // 如果不是Map或为null，创建新Map
                nextObj = new HashMap<String, Object>();
                currentMap.put(part, nextObj);
            }

            // 更新当前Map
            @SuppressWarnings("unchecked")
            Map<String, Object> nextMap = (Map<String, Object>) nextObj;
            currentMap = nextMap;
        }

        // 设置最后一级的值
        currentMap.put(pathParts[pathParts.length - 1], value);
    }

    private ParamsMap convertToParamsMap(Map<?, ?> map) {
        ParamsMap result = new ParamsMap();
        if (MapUtils.isNotEmpty(map)) {
            result.putAll(map);
        }
        return result;
    }
}
