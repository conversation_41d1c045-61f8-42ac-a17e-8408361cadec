bootstrap.servers=10.171.2.38:9092,10.171.2.182:9092,10.171.2.154:9092
listener.core.threads=10
listener.max.threads=30
listener.queue.capacity=3000
# flink source
consumer.currency = 10
consumer.core.threads=10
consumer.max.threads=30
consumer.queue.capacity=3000

riskguard.decide.finish.topic=riskguard_event

riskguard.async.scene.topic=riskguard_event_async

riskguard.checklist.topic=riskguard_checklist_event

# accompany
accompany.consumer.currency = 1
accompany.consumer.core.threads=3
accompany.consumer.max.threads=5
accompany.consumer.queue.capacity=3000

# checklist
checklist.consumer.currency = 1
checklist.consumer.core.threads=3
checklist.consumer.max.threads=5
checklist.consumer.queue.capacity=3000


# persistence
persistence.consumer.currency = 3
persistence.consumer.core.threads=3
persistence.consumer.max.threads=30
persistence.consumer.queue.capacity=3000

# orchestration
orchestration.consumer.currency = 1
orchestration.consumer.core.threads=3
orchestration.consumer.max.threads=10
orchestration.consumer.queue.capacity=3000