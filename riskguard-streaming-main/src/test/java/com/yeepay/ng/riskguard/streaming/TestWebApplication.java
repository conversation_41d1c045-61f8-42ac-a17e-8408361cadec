/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming;

import com.yeepay.g3.utils.common.InitializeUtils;
import com.yeepay.springframework.boot.annotation.EnableSoa;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/2/26 4:37 下午
 */
@SpringBootApplication(scanBasePackages = {"com.yeepay.ng.riskguard.streaming.*"},
        exclude = {
                DataSourceAutoConfiguration.class,
                TransactionAutoConfiguration.class})
@EnableSoa
public class TestWebApplication {
    public static void main(String[] args) {
        InitializeUtils.initComponents();
        SpringApplication.run(TestWebApplication.class, args);
    }
}
