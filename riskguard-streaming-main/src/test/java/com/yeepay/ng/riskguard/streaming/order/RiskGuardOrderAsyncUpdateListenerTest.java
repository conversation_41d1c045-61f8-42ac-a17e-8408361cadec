package com.yeepay.ng.riskguard.streaming.order;

import com.yeepay.ng.riskguard.streaming.SpringBootTests;
import com.yeepay.ng.riskguard.streaming.infrastructure.factory.UpdateHandleStrategy;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/4/3 2:18 下午
 */
public class RiskGuardOrderAsyncUpdateListenerTest extends SpringBootTests {
    @Autowired
    private UpdateHandleStrategy updateHandleStrategy;

    @Test
    public void test() {
        String message = "{\"orderNo\":\"2025040717405981951781355081768\",\"bizCode\":\"GPTransfer\",\"scenarioCode\":\"QM\",\"stage\":\"INQUIRY\",\"requestNo\":\"1744018858818\",\"requestAt\":\"2025-04-07 09:40:59\",\"requestParams\":{\"bln\":\"***********\",\"dob\":\"2004-04-01\",\"nat\":\"中国\",\"idNo\":\"11000200404011234\",\"opNo\":\"***********\",\"extInfo\":\"拓展字段\",\"brandName\":\"CC\",\"eventType\":\"商场\",\"localName\":\"豆腐串\",\"notifyUrl\":\"https://qaboss.yeepay.com\",\"occurTime\":\"2025-04-07 17:40:59\",\"requestId\":\"1744018858818\",\"merchantId\":\"1101234567\",\"paramValue\":\"***************\",\"registerNo\":\"***********\",\"checkObject\":\"反反复复\",\"englishName\":\"English name\",\"requestTime\":\"2025-04-07 17:40:58\",\"customerType\":\"个体\",\"registerArea\":\"美国\",\"extReqDTOList\":[{\"area\":\"中国\",\"name\":\"名称\",\"enName\":\"name\",\"address\":\"地址\",\"birthday\":\"2022-01-01\",\"idNumber\":\"110111202201011234\",\"infoType\":\"smn\",\"registerNo\":\"1111111111\",\"nationality\":\"中国\",\"registerAtea\":\"中国\",\"businessAddress\":\"办公地址\"}],\"businessAddress\":\"办公地址\",\"registerAddress\":\"三中\",\"executorClassName\":\"食品\",\"executorMethodName\":\"方法名\",\"analysisServiceName\":\"payment\",\"analysisServiceMethod\":\"零售\"},\"indicatorValues\":{},\"hitNameList\":[{\"variableCode\":\"WuTestBlack\",\"code\":\"B_WuTestBlack\",\"type\":\"BLACK\",\"details\":[{\"id\":417,\"version\":1,\"categoryValues\":{\"riskType\":[\"电信欺诈\"]}}]}],\"variables\":{\"bizCode\":\"GPTransfer\",\"orderNo\":\"2025040717405981951781355081768\",\"requestAt\":\"2025-04-07 17:40:59\",\"requestNo\":\"1744018858818\",\"stageCode\":\"INQUIRY\",\"asyncRequest\":true,\"decideResult\":\"ASYNC\",\"scenarioCode\":\"QM\",\"inGrayNamelist\":false},\"functionVariables\":{\"IpInfoAnalysis\":{\"success\":true,\"data\":{\"ipCity\":\"北京\",\"ip\":\"***************\",\"ipCountry\":\"中国\",\"success\":true,\"ipProvince\":\"北京\"}}},\"environment\":\"PRODUCT\",\"policyCode\":\"GPTransfer:QM:INQUIRY\",\"policyVersion\":9,\"decideType\":\"BLOCK\",\"notifyUrl\":\"https://qaboss.yeepay.com\"}";
        updateHandleStrategy.handle(message);
    }
}
