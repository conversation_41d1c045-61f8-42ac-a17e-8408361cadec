/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming;

import com.yeepay.ng.riskguard.commons.enums.StageType;
import com.yeepay.ng.riskguard.rules.facade.indicator.dto.IndicatorConfigDTO;
import com.yeepay.ng.riskguard.rules.facade.indicator.dto.IndicatorDTO;
import com.yeepay.ng.riskguard.streaming.app.cache.IndicatorRefreshableCache;
import com.yeepay.ng.riskguard.streaming.domain.gateway.IndicatorGateway;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/25 15:37
 */
public class IndicatorSendToFlinkTest extends SpringBootTests{
    @Autowired
    private IndicatorRefreshableCache springCache;
    @Test
    public void testSpring() throws Exception {
        Assert.assertTrue(springCache.containsIndicators("GPT","WuIndicatorTest","INQUIRY"));
    }
    @Test
    public void testMock() throws Exception {
        IndicatorGateway indicatorGateway = Mockito.mock(IndicatorGateway.class);
        Mockito.when(indicatorGateway.queryIndicators(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(buildTestData());

        IndicatorRefreshableCache indicatorRefreshableCache = new IndicatorRefreshableCache();
        indicatorRefreshableCache.setIndicatorGateway(indicatorGateway);
        Assert.assertTrue(indicatorRefreshableCache.containsIndicators("test", "test", StageType.INQUIRY.name()));

        Assert.assertFalse(indicatorRefreshableCache.containsIndicators("test", "test", StageType.CONFIRM.name()));

        Assert.assertFalse(indicatorRefreshableCache.containsHitRulesAcc("test", "test", StageType.CONFIRM.name()));

    }

    private List<IndicatorDTO> buildTestData() {
        IndicatorDTO indicatorDTO = new IndicatorDTO();
        indicatorDTO.setCode("test");
        indicatorDTO.setScenarioCode("test");
        indicatorDTO.setAccStage(StageType.INQUIRY.name());
        indicatorDTO.setDimensions("customerNo");
        indicatorDTO.setConfig(IndicatorConfigDTO.builder()
                .aggregateField("requestNo")
                .aggregateFunc("COUNT")
                .dimensionField(new ArrayList() {
                    {
                        add("customerNo");
                    }
                })
                .build());
        List<IndicatorDTO> indicatorDTOList = new ArrayList<>();
        indicatorDTOList.add(indicatorDTO);



        return indicatorDTOList;
    }
}