package com.yeepay.ng.riskguard.streaming.order;

import com.yeepay.ng.riskguard.streaming.SpringBootTests;
import com.yeepay.ng.riskguard.streaming.listener.OrchestrationEventListener;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.support.Acknowledgment;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/4/9 11:07 上午
 */
public class OrchestrationEventListenerTest extends SpringBootTests {
    @Autowired
    private OrchestrationEventListener orchestrationEventListener;

    @Test
    public void test() {
        Acknowledgment ack = Mockito.mock(Acknowledgment.class);
        String message = "{\"orderNo\":\"2025041015054783021653413597222\",\"bizCode\":\"GPTransfer\",\"scenarioCode\":\"QM\",\"stage\":\"INQUIRY\",\"requestNo\":\"1744268747480\",\"requestAt\":\"2025-04-10 07:05:47\",\"requestParams\":{\"bln\":\"***********\",\"dob\":\"2004-04-01\",\"nat\":\"中国\",\"idNo\":\"11000200404011234\",\"opNo\":\"***********\",\"extInfo\":\"拓展字段\",\"brandName\":\"CC\",\"eventType\":\"商场\",\"localName\":\"豆腐串\",\"notifyUrl\":\"https://qaboss.yeepay.com\",\"occurTime\":\"2025-04-10 15:05:47\",\"requestId\":\"1744268747484\",\"merchantId\":\"***********\",\"paramValue\":\"***************\",\"registerNo\":\"***********\",\"checkObject\":\"反反复复\",\"englishName\":\"English name\",\"requestTime\":\"2025-04-10 15:05:47\",\"customerType\":\"个体\",\"registerArea\":\"美国\",\"extReqDTOList\":[{\"area\":\"中国\",\"name\":\"名称\",\"enName\":\"name\",\"address\":\"地址\",\"birthday\":\"2022-01-01\",\"idNumber\":\"110111202201011234\",\"infoType\":\"smn\",\"registerNo\":\"1111111111\",\"nationality\":\"中国\",\"registerAtea\":\"中国\",\"businessAddress\":\"办公地址\"}],\"businessAddress\":\"办公地址\",\"registerAddress\":\"三中\",\"executorClassName\":\"食品\",\"executorMethodName\":\"方法名\",\"analysisServiceName\":\"payment\",\"analysisServiceMethod\":\"零售\"},\"indicatorValues\":{},\"variables\":{\"bizCode\":\"GPTransfer\",\"orderNo\":\"2025041015054783021653413597222\",\"requestAt\":\"2025-04-10 15:05:47\",\"requestNo\":\"1744268747480\",\"stageCode\":\"INQUIRY\",\"asyncRequest\":true,\"decideResult\":\"ASYNC\",\"scenarioCode\":\"QM\",\"inGrayNamelist\":false,\"decideType\":\"BLOCK\"},\"functionVariables\":{\"ExecuteRealLnService\":{\"success\":true,\"data\":{\"prod\":\"RECHARGE\",\"errorDescription\":\"未命中LN名单\",\"lnResultCode\":\"1\",\"biz\":\"GPT_BUSINESS\",\"merchantId\":\"***********\",\"success\":true,\"requestId\":\"2025041015054783021653413597222\",\"memberId\":\"***********\",\"status\":\"200\"},\"async\":false}},\"hitRules\":[{\"ruleCode\":\"T8bMpnhzRJS85XOabDuP9A\",\"ruleVersion\":20,\"enabled\":true,\"decideType\":\"BLOCK\"}],\"failedRules\":[],\"environment\":\"PRODUCT\",\"policyCode\":\"GPTransfer:QM:INQUIRY\",\"policyVersion\":20,\"decideType\":\"BLOCK\",\"notifyUrl\":\"https://qaboss.yeepay.com\"}";
        orchestrationEventListener.onMessage(message,"update", ack);
    }

}
