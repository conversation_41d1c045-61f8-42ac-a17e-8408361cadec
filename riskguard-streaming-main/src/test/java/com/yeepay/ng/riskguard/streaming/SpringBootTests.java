/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming;

import com.yeepay.g3.utils.config.ConfigurationUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/4 7:11 下午
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestWebApplication.class)
public class SpringBootTests {

    @BeforeClass
    public static void setUpBeforeClass() {
        ConfigurationUtils.init();
        RemoteServiceFactory.init();
    }
}
