/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.order;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.g3.utils.config.ConfigurationUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.ng.riskguard.commons.enums.OrderActualStatus;
import com.yeepay.ng.riskguard.commons.enums.OrderProcessStatus;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.streaming.SpringBootTests;
import com.yeepay.ng.riskguard.streaming.app.executor.ChecklistExecutor;
import com.yeepay.ng.riskguard.streaming.domain.entity.DecideLog;
import com.yeepay.ng.riskguard.streaming.domain.entity.Order;
import com.yeepay.ng.riskguard.streaming.domain.model.ChecklistMessage;
import com.yeepay.ng.riskguard.streaming.domain.service.OrderService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/25 15:58
 */
public class OrderServiceTest extends SpringBootTests {
    @Autowired
    private OrderService orderService;

    @Autowired
    private ChecklistExecutor checklistExecutor;

    @Test
    public void test() {
        Order order = new Order();
        order.setOrderNo("12345678901234567890123456789014");
        order.setRequestNo("12345678901234567890123456789012");
        order.setBatchNo("1");
        order.setEnvironment("正式");
        order.setPolicyCode("fwb_test");
        order.setPolicyVersion(1L);
        order.setRequestAt(new java.util.Date());
        order.setBizCode("GPT");
        order.setScenarioCode("fwb_test");
        order.setRequestParams(new java.util.HashMap<>());
        order.setDecideType("PASS");
        order.setActualStatus(OrderActualStatus.SUCCESS);
        order.setProcessStatus(OrderProcessStatus.SUCCESS);
        order.setErrorMsg("error");

        DecideLog decideLog = new DecideLog();
        order.setDecideLog(decideLog);
        orderService.create(order, false);
    }

    private static JsonMapper JSON_MAPPER = JsonMapper.nonNullMapper();

    JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    @Test
    public void testChecklist() throws Exception {
        ConfigUtils.init();
//        RemoteServiceFactory.init();
        String str = "{\n" +
                "  \"bizCode\": \"GPT\",\n" +
                "  \"scenarioCode\": \"WuTestConfirm\",\n" +
                "  \"stage\": \"INQUIRY\",\n" +
                "  \"orderNo\": \"2025042310194587839434193961000\",\n" +
                "  \"requestNo\": \"*************\",\n" +
                "  \"handleResult\": \"AT_RISK\",\n" +
                "  \"investigationInfo\": \"调查信息\",\n" +
                "  \"checklistNo\": \"核查单号\",\n" +
                "  \"relatedOrderList\": [\n" +
                "    {\n" +
                "      \"bankOrderId\": \"ER59MLM19\",\n" +
                "      \"rejectReasonList\": [\n" +
                "        {\n" +
                "          \"inReasonDescription\": \"订单关联有误，请重新关联正确的订单\",\n" +
                "          \"reasonDescription\": \"订单关联有误，请核对后重新关联正确的订单\",\n" +
                "          \"reasonDescriptionCode\": \"RELATED_ERROR\",\n" +
                "          \"rejectReasonCode\": \"ORDER_RELATED_ERROR\",\n" +
                "          \"rejectReasonText\": \"订单关联有误\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"relatedOrderId\": 8810,\n" +
                "      \"transactionOrderId\": \"603365909214571735\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        str = "{\n" +
                "\t\"bizCode\": \"GPT\",\n" +
                "\t\"checklistNo\": \"SK202504250076\",\n" +
                "\t\"handleResult\": \"NO_RISK\",\n" +
                "\t\"orderNo\": \"requestId_emeter_0124555337\",\n" +
                "\t\"productCode\": \"GPT_RECHARGE\",\n" +
                "\t\"requestNo\": \"sequenceId_recharge_8250800018\",\n" +
                "\t\"relatedOrderList\": [{\n" +
                "\t\t\"bankOrderId\": \"bankOrderId_86054204\",\n" +
                "\t\t\"rejectReasonList\": [{\n" +
                "\t\t\t\"inReasonDescription\": \"订单关联有误，请重新关联正确的订单\",\n" +
                "\t\t\t\"reasonDescription\": \"订单关联有误，请核对后重新关联正确的订单\",\n" +
                "\t\t\t\"reasonDescriptionCode\": \"RELATED_ERROR\",\n" +
                "\t\t\t\"rejectReasonCode\": \"ORDER_RELATED_ERROR\",\n" +
                "\t\t\t\"rejectReasonText\": \"订单关联有误\"\n" +
                "\t\t}],\n" +
                "\t\t\"relatedOrderId\": 15371,\n" +
                "\t\t\"transactionOrderId\": \"transactionOrderId_70260380\"\n" +
                "\t}, {\n" +
                "\t\t\"bankOrderId\": \"bankOrderId_76554424\",\n" +
                "\t\t\"rejectReasonList\": [],\n" +
                "\t\t\"relatedOrderId\": 15372,\n" +
                "\t\t\"transactionOrderId\": \"transactionOrderId_18665313\"\n" +
                "\t}]\n" +
                "}";
        ChecklistMessage checklistMessage = JSON_PARSER.parse(str, ChecklistMessage.class);
        checklistExecutor.report(checklistMessage);
    }
}
