/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.order;

import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.gateway.facade.event.DecideFinishEvent;
import org.junit.Test;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/21 21:56
 */
public class TestJson {
    private static final JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();
    @Test
    public void test() {
        String json = "{\n" +
                "  \"bizCode\" : \"GPT\",\n" +
                "  \"scenarioCode\" : \"pan\",\n" +
                "  \"policyCode\" : \"GPT:pan:INQUIRY\",\n" +
                "  \"policyVersion\" : 4,\n" +
                "  \"environment\" : \"PRODUCT\",\n" +
                "  \"orderNo\" : \"2025052318310499363604544532481\",\n" +
                "  \"requestNo\" : \"1747996259886\",\n" +
                "  \"params\" : {\n" +
                "    \"targetCnyAmount\" : 100.02,\n" +
                "    \"amount\" : 100,\n" +
                "    \"merchantId\" : \"12113531\",\n" +
                "    \"tradeType\" : \"Pay\",\n" +
                "    \"tradeDate\" : \"2025-05-23 17:07:28\",\n" +
                "    \"subMerchantId\" : \"12113531\",\n" +
                "    \"occurTime\" : \"2025-05-23 17:07:28\"\n" +
                "  },\n" +
                "  \"decideType\" : \"REVIEW\",\n" +
                "  \"requestAt\" : \"2025-05-23 18:31:04\",\n" +
                "  \"notifyStatus\" : 1,\n" +
                "  \"decideLog\" : {\n" +
                "    \"skipIndicatorQuery\" : false,\n" +
                "    \"skipNameListEngine\" : true,\n" +
                "    \"skipRuleEngine\" : false,\n" +
                "    \"indicatorValues\" : {\n" +
                "      \"amountSum\" : 300.06\n" +
                "    },\n" +
                "    \"hitRules\" : [ {\n" +
                "      \"ruleCode\" : \"KZIdWMDkSHuhd2t3XlNX_g\",\n" +
                "      \"ruleVersion\" : 4,\n" +
                "      \"enabled\" : true,\n" +
                "      \"decideType\" : \"REVIEW\"\n" +
                "    } ],\n" +
                "    \"failedRules\" : [ ],\n" +
                "    \"hitNameList\" : [ ],\n" +
                "    \"variables\" : {\n" +
                "      \"decideResult\" : \"REVIEW\",\n" +
                "      \"orderNo\" : \"2025052318310499363604544532481\",\n" +
                "      \"asyncRequest\" : false,\n" +
                "      \"bizCode\" : \"GPT\",\n" +
                "      \"inGrayNamelist\" : false,\n" +
                "      \"scenarioCode\" : \"pan\",\n" +
                "      \"requestNo\" : \"1747996259886\",\n" +
                "      \"stageCode\" : \"INQUIRY\",\n" +
                "      \"requestAt\" : \"2025-05-23 18:31:04\"\n" +
                "    },\n" +
                "    \"functionVariables\" : {\n" +
                "      \"queryCustomerInfo\" : {\n" +
                "        \"success\" : true,\n" +
                "        \"data\" : {\n" +
                "          \"industryPerformance\" : \"全球出行行业线\",\n" +
                "          \"memberType\" : \"测试\",\n" +
                "          \"industryDomain\" : \"\"\n" +
                "        }\n" +
                "      }\n" +
                "    },\n" +
                "    \"actionResult\" : {\n" +
                "      \"RULE#KZIdWMDkSHuhd2t3XlNX_g#4\" : {\n" +
                "        \"ASSIGN\" : {\n" +
                "          \"testFun2\" : \"测试\",\n" +
                "          \"testFun\" : \"memberType\"\n" +
                "        }\n" +
                "      }\n" +
                "    }\n" +
                "  },\n" +
                "  \"responseResult\" : {\n" +
                "    \"testFun2\" : \"测试\",\n" +
                "    \"testFun\" : \"memberType\"\n" +
                "  }\n" +
                "}";
        DecideFinishEvent event = JSON_PARSER.parse(json, DecideFinishEvent.class);
        System.out.println(event);
    }
}