package com.yeepay.ng.riskguard.streaming.order;

import com.yeepay.ng.riskguard.streaming.SpringBootTests;
import com.yeepay.ng.riskguard.streaming.listener.DatasourcePersistenceListener;
import com.yeepay.ng.riskguard.streaming.listener.OrchestrationEventListener;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.support.Acknowledgment;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/4/9 2:56 下午
 */
public class DatasourcePersistenceListenerTest extends SpringBootTests {
    @Autowired
    private DatasourcePersistenceListener datasourcePersistenceListener;

    @Test
    public void test() {
        Acknowledgment ack = Mockito.mock(Acknowledgment.class);
        String message = "{\"bizCode\":\"GPT\",\"scenarioCode\":\"WuTestConfirm\",\"policyCode\":\"GPT:WuTestConfirm:INQUIRY\",\"policyVersion\":14,\"environment\":\"PRODUCT\",\"orderNo\":\"2025040911193582927035284054016\",\"requestNo\":\"1744168775589\",\"params\":{\"amount\":1000.0,\"product\":\"X\",\"prepaidAmount\":3000,\"testObjectObj\":{\"bandCard\":\"1101234567\",\"goods\":[\"酵素乳0\"],\"customerNo\":\"12345\",\"merchantNo\":\"1101234567\"},\"occurTime\":\"2025-04-09 11:19:36\",\"requestMerchantNo\":\"111\",\"requestNo\":\"1744168775589\",\"sequenceId\":\"c319cb68-059e-457c-a449-e345177f63dc\",\"userId\":\"riskTest123456789\",\"success\":true,\"orderDate\":\"2025-04-09 11:19:35\",\"hash\":\"35dRfg3bWZXdGjW1vKLMu28AmRSWvSid9o\",\"merchantNo\":\"1101234567\",\"status\":\"SUCCESS\"},\"decideType\":\"BLOCK\",\"requestAt\":\"2025-04-09 11:19:35\",\"notifyStatus\":1,\"decideLog\":{\"skipIndicatorQuery\":false,\"skipNameListEngine\":false,\"skipRuleEngine\":true,\"indicatorValues\":{\"sunDiv_A\":0.0,\"testAccCur\":541000.0,\"sunDiv_B\":0.0,\"testSum\":0.0},\"hitNameList\":[{\"variableCode\":\"wuBlack\",\"code\":\"B_WuBlack\",\"type\":\"BLACK\",\"details\":[{\"id\":416,\"version\":1,\"categoryValues\":{\"riskType\":[\"电信欺诈\"]}}]}],\"hitNameListRules\":[{\"policyNameListCode\":\"GPT:WuTestConfirm:INQUIRY:14:aNcIKp7gRHqQJGqBcHo5Gg\",\"nameListType\":\"BLACK\",\"decideType\":\"BLOCK\"}],\"variables\":{\"decideResult\":\"BLOCK\",\"orderNo\":\"2025040911193582927035284054016\",\"asyncRequest\":false,\"bizCode\":\"GPT\",\"inGrayNamelist\":false,\"scenarioCode\":\"WuTestConfirm\",\"requestNo\":\"1744168775589\",\"stageCode\":\"INQUIRY\",\"requestAt\":\"2025-04-09 11:19:35\"}}}";
        datasourcePersistenceListener.onMessage(message,"update", ack);
    }
}
