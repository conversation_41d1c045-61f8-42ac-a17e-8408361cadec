<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO"
               packages="com.yeepay.g3.utils"><!--packages参数告诉log4j2还需要额外加载哪个包下的Log4j plugin，其中YeepayMessagePatternConverter即为定制的plugin,负责输出的日志带GUID -->
    <Appenders>
        <Console name="STDOUT" target="SYSTEM_OUT">
            <!-- <PatternLayout pattern="%d %-5p %c:%L [%t] - %m%n" /> -->
            <!--<PatternLayout pattern="%d %-5p %c [%t] - %Y%n"/>-->
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} - %C{3} -%-4r [%t] %-5p %x - %msg%n%throwable"/>
        </Console>
    </Appenders>
    <Loggers>
        <Logger name="com.yeepay.ng.riskguard" level="INFO"/>
        <Root level="INFO"><!-- 缺省日志级别，如果package有定制级别，则按package的定制级别走，即使package级别更低 -->
            <AppenderRef ref="STDOUT"/>
            <!--<AppenderRef ref="fluent-app"/>-->
        </Root>
    </Loggers>
</Configuration>
