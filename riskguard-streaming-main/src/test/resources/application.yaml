spring:
  application:
    name: riskguard-streaming
  datasource:
    dynamic:
      strict: false
      primary: master
      datasource:
        master:
          type: com.yeepay.g3.utils.common.datasource.impl.DruidPooledDataSource
          poolName: RISKGUARD
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_empty
    locale: zh_CN
    time-zone: GMT+8
  profiles:
    active: dev
server:
  tomcat:
    uri-encoding: UTF-8
    basedir: /var/tmp
    background-processor-delay: 30
    accesslog:
      enabled: true
      buffered: true
      directory: ./log
      file-date-format: .yyyy-MM-dd
      pattern: '%{X-Forwarded-For}i %h %l %u %D %t "%m %U %{_method}i" %s %b'
      prefix: access_log
      rename-on-rotate: false
      request-attributes-enabled: false
      rotate: true
      suffix: .log
    max-http-form-post-size: 10485760
    threads:
      max: 800
    connection-timeout: 20000
  servlet:
    encoding:
      force: true
    context-parameters:
      soa_app_name: riskguard-streaming
---
server:
  port: 8066
  tomcat:
    remoteip:
      port-header: X-Forwarded-Port
      protocol-header: X-Forwarded-Proto
      remote-ip-header: X-Forwarded-For
logging:
  config: classpath:log4j2.xml
spring:
  config:
    activate:
      on-profile: dev
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: ALWAYS
---
spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  config:
    activate:
      on-profile: prod
  web:
    resources:
      chain:
        strategy:
          content:
            enabled: true
        cache: true
        compressed: true
server:
  port: 8080
  compression:
    enabled: true
    min-response-size: 2048
    mime-types: text/html,text/xml,text/javascript,application/javascript,text/css,text/plain
logging:
  config: /apps/commoncfg/log4j2.xml