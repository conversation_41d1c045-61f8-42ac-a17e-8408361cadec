# 核查结果场景编排功能实现说明

## 功能概述

根据您提供的流程图，我们在核查结果监听器（ChecklistEventListener）中添加了场景编排触发逻辑。该功能实现了以下流程：

1. **接收核查结果消息** - 监听核查结果消息
2. **订单决策更新、记录核查结果** - 更新订单状态和记录核查单详情
3. **查询核查结果阶段缺失的场景编排配置** - 根据策略编码和版本查询编排配置
4. **判断配置场景编排** - 检查是否有配置的编排规则
5. **触发场景编排或回调通知** - 如果有配置则触发编排，否则直接回调通知

## 实现细节

### 修改的文件

1. **ChecklistExecutorImpl.java** - 核查结果执行器实现类
   - 添加了 `PolicyOrchestrationService` 依赖注入
   - 在 `report` 方法中添加了场景编排触发调用
   - 新增了 `triggerOrchestrationIfConfigured` 方法
   - 新增了 `processChecklistOrchestrationHandler` 方法

### 核心方法说明

#### triggerOrchestrationIfConfigured 方法
```java
private void triggerOrchestrationIfConfigured(ChecklistMessage checklistMessage, Order order)
```
- **功能**: 查询核查结果阶段的场景编排配置，判断是否配置场景编排
- **参数**: 
  - `checklistMessage`: 核查结果消息
  - `order`: 订单信息
- **逻辑**:
  1. 根据订单的策略编码和版本查询编排配置
  2. 如果找到配置，则遍历处理每个编排配置
  3. 如果没有找到配置，则记录日志并返回

#### processChecklistOrchestrationHandler 方法
```java
private void processChecklistOrchestrationHandler(PolicyOrchestrationList handler, ChecklistMessage checklistMessage, Order order)
```
- **功能**: 处理单个场景编排配置
- **参数**:
  - `handler`: 场景编排配置
  - `checklistMessage`: 核查结果消息
  - `order`: 订单信息
- **逻辑**:
  1. 检查编排配置是否启用
  2. 根据执行方式（同步/异步）处理编排逻辑
  3. 记录处理日志

### 执行流程

1. **核查结果消息接收**: `ChecklistEventListener.onMessage()` 接收Kafka消息
2. **核查结果处理**: `ChecklistExecutorImpl.report()` 处理核查结果
3. **订单更新**: 更新订单的核查结果状态
4. **核查单记录**: 如果有核查单信息，则记录核查详情
5. **场景编排触发**: 调用 `triggerOrchestrationIfConfigured()` 查询并触发场景编排
6. **业务通知**: 最后执行 `notifyCustomers()` 进行业务通知

### 配置查询逻辑

场景编排配置查询基于以下条件：
- **策略编码** (`policyCode`): 从订单信息中获取
- **策略版本** (`policyVersion`): 从订单信息中获取

查询到的编排配置会根据以下条件进行过滤：
- **启用状态** (`enabled`): 只处理启用的配置（enabled = 1）
- **执行方式** (`executeMethod`): 支持同步（SYNC）和异步（ASYNC）两种方式

### 日志记录

实现中添加了详细的日志记录，包括：
- 开始查询场景编排配置
- 查询结果（找到的配置数量）
- 处理每个配置的详细信息
- 配置启用状态检查
- 执行方式处理
- 异常处理

### 扩展点

1. **SPEL表达式匹配**: 目前预留了TODO，可以参考 `OrchestrationEventListener` 中的实现添加表达式匹配逻辑
2. **同步执行逻辑**: 目前为空实现，可以根据需要添加具体的同步处理逻辑
3. **异步执行逻辑**: 目前为空实现，可以根据需要添加具体的异步处理逻辑

## 使用说明

该功能会在核查结果处理过程中自动触发，无需额外配置。只要在策略编排配置表中配置了相应的编排规则，系统就会自动查询并执行相应的场景编排逻辑。

## 注意事项

1. **异常处理**: 场景编排过程中的异常不会影响主流程，会记录错误日志但不会抛出异常
2. **性能考虑**: 查询编排配置是同步操作，如果配置较多可能会影响处理性能
3. **扩展性**: 当前实现为基础框架，具体的编排执行逻辑需要根据业务需求进一步完善

## 后续优化建议

1. 添加SPEL表达式匹配功能，提高编排规则的灵活性
2. 实现具体的同步和异步执行逻辑
3. 添加编排配置缓存，提高查询性能
4. 增加更详细的监控和指标统计
