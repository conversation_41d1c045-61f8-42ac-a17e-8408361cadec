<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yeepay.ng.riskguard</groupId>
        <artifactId>riskguard-streaming-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>riskguard-streaming-app</artifactId>
    <packaging>jar</packaging>

    <name>riskguard-streaming-app</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-rules-commons</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-gateway-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-engine-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-streaming-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-streaming-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.ng.riskguard</groupId>
            <artifactId>riskguard-engine-app</artifactId>
        </dependency>
    </dependencies>
</project>
