/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.app.executor.impl;

import cn.hutool.core.map.MapUtil;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.ng.riskguard.commons.enums.DecideType;
import com.yeepay.ng.riskguard.commons.enums.NotifyStatus;
import com.yeepay.ng.riskguard.commons.enums.StageType;
import com.yeepay.ng.riskguard.commons.utils.OrderNoUtils;
import com.yeepay.ng.riskguard.engine.facade.rule.dto.data.RuleExecuteItemDTO;
import com.yeepay.ng.riskguard.gateway.facade.dto.confirm.ConfirmCommand;
import com.yeepay.ng.riskguard.gateway.facade.dto.confirm.ConfirmExtCommand;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideCommand;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideExtCommand;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideLogDTO;
import com.yeepay.ng.riskguard.gateway.facade.event.ConfirmDecideFinishEvent;
import com.yeepay.ng.riskguard.gateway.facade.event.DecideFinishEvent;
import com.yeepay.ng.riskguard.gateway.spec.exception.RiskguardExceptionEnum;
import com.yeepay.ng.riskguard.streaming.app.cache.PolicyVersionRefreshableCache;
import com.yeepay.ng.riskguard.streaming.app.cache.RequestParamRefreshableCache;
import com.yeepay.ng.riskguard.streaming.app.cache.key.PolicyVersionCacheKey;
import com.yeepay.ng.riskguard.streaming.app.cache.key.RequestParamKey;
import com.yeepay.ng.riskguard.streaming.app.converter.NameListDTOConverter;
import com.yeepay.ng.riskguard.streaming.app.executor.RiskguardExecutor;
import com.yeepay.ng.riskguard.streaming.app.service.impl.AccompanySender;
import com.yeepay.ng.riskguard.streaming.domain.entity.NameListDecide;
import com.yeepay.ng.riskguard.streaming.domain.gateway.backend.ParamMappingDTO;
import com.yeepay.ng.riskguard.streaming.domain.gateway.decide.DecideContextDTO;
import com.yeepay.ng.riskguard.streaming.domain.gateway.decide.DecideResultDTO;
import com.yeepay.ng.riskguard.streaming.domain.gateway.decide.RuleExecuteItem;
import com.yeepay.ng.riskguard.streaming.domain.gateway.policy.PolicyVersionDTO;
import com.yeepay.ng.riskguard.streaming.domain.gateway.policy.PolicyVersionDetailDTO;
import com.yeepay.ng.riskguard.streaming.domain.utils.ObjectFieldValidator;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/26 10:22
 */
@Slf4j
@Component
public class RiskguardExecutorImpl implements RiskguardExecutor {

    @Setter(onMethod_ = @Autowired)
    private RequestParamRefreshableCache requestParamCache;

    @Setter(onMethod_ = @Autowired)
    private PolicyVersionRefreshableCache policyVersionRefreshableCache;

    @Setter(onMethod_ = @Autowired)
    private AccompanySender accompanySender;

    @Override
    public void decide(DecideCommand decideCommand, DecideExtCommand decideExtCommand) {
        String environment = decideExtCommand.getEnvironment();

        // 对查询参数进行校验
        List<ParamMappingDTO> decideParams = queryRequestParamFromCache(decideCommand.getBizCode(), decideCommand.getScenarioCode(), StageType.INQUIRY);
//        validateParams(decideCommand.getParams(), decideParams);

        // 查询策略版本信息
        PolicyVersionDTO policyVersion = queryPolicyVersionFromCache(decideCommand.getBizCode(), decideCommand.getScenarioCode(), StageType.INQUIRY, environment);
        List<PolicyVersionDetailDTO> policyVersionDTOS = new ArrayList<>();
        if (null != policyVersion) {
            policyVersionDTOS = policyVersion.getVersions().get(StageType.INQUIRY.name());
        }
        // 没有可执行的策略版本直接返回通过
        if (CollectionUtils.isEmpty(policyVersionDTOS)) {
            log.warn("decide policy version is empty,scenarioCode:{},environment:{}", decideCommand.getScenarioCode(), environment);
            return;
        }
        accompanySender.send(buildDecideFinishEvent(decideCommand, decideExtCommand, environment, new DecideContextDTO(), DecideResultDTO.builder().decideType(DecideType.PASS.name()).build(), null));

    }

    @Override
    public void confirm(ConfirmCommand confirmCommand, ConfirmExtCommand confirmExtCommand) {
        String environment = confirmExtCommand.getEnvironment();

        // 对查询参数进行校验
        List<ParamMappingDTO> confirmParams = queryRequestParamFromCache(confirmCommand.getBizCode(), confirmCommand.getScenarioCode(), StageType.CONFIRM);
        validateConfirmParams(confirmCommand.getParams(), confirmParams);

        // 查询策略版本信息
        PolicyVersionDTO policyVersion = queryPolicyVersionFromCache(confirmCommand.getBizCode(), confirmCommand.getScenarioCode(), StageType.CONFIRM, environment);
        List<PolicyVersionDetailDTO> policyVersionDTOS = new ArrayList<>();
        if (null != policyVersion) {
            policyVersionDTOS = policyVersion.getVersions().get(StageType.CONFIRM.name());
        }
        // 没有可执行的策略版本直接返回通过
        if (CollectionUtils.isEmpty(policyVersionDTOS)) {
            log.warn("decide policy version is empty,scenarioCode:{},environment:{}", confirmCommand.getScenarioCode(), environment);
            return;
        }
        accompanySender.send(buildConfirmCommand(confirmCommand, confirmExtCommand, environment, new DecideContextDTO(), null, null));
    }

    private DecideFinishEvent buildDecideFinishEvent(DecideCommand decideCommand, DecideExtCommand extCommand, String environment, DecideContextDTO decideContext, DecideResultDTO ruleDecide, NameListDecide nameListDecide) {
        DecideFinishEvent event = new DecideFinishEvent();
        event.setEnvironment(environment);
        event.setBizCode(decideCommand.getBizCode());
        event.setScenarioCode(decideCommand.getScenarioCode());
        event.setRequestNo(decideCommand.getRequestNo());
//        event.setRequestAt(OrderNoUtils.getFormatedRequestAt(decideContext.getOrderNo()));
        event.setParams(decideCommand.getParams());
        event.setOrderNo(decideContext.getOrderNo());

        DecideLogDTO decideLog = new DecideLogDTO();
        decideLog.setSkipRuleEngine(decideContext.getSkipRuleEngine());
        decideLog.setSkipIndicatorQuery(decideContext.getSkipIndicatorQuery());
        decideLog.setSkipNameListEngine(decideContext.getSkipNameListEngine());
        decideLog.setIndicatorValues(decideContext.getIndicators());
        decideLog.setVariables(decideContext.getSystemParams());
        if (null != ruleDecide) {
            decideLog.setHitRules(convertRules(ruleDecide.getHitRules()));
            decideLog.setFailedRules(convertRules(ruleDecide.getFailedRules()));
            decideLog.setFunctionVariables(ruleDecide.getFunctionVariables());
        }

        if (null != nameListDecide) {
            decideLog.setHitNameList(NameListDTOConverter.INSTANCE.to(nameListDecide.getNameList()));
        }
        event.setDecideLog(decideLog);
        event.setParams(decideCommand.getParams());
        String decideType = null != decideContext.getFinalDecide() ? decideContext.getFinalDecide().getDecideType() : ruleDecide.getDecideType();
        event.setDecideType(decideType);
        if (null != extCommand) {
            event.setPersistent(extCommand.getPersistent());
            event.setBatchNo(extCommand.getBatchNo());
        }
        event.setPolicyCode(decideContext.getPolicyCode());
        event.setPolicyVersion(decideContext.getPolicyVersion());
        event.setNotifyStatus(NotifyStatus.SUCCESS.getValue());
        return event;
    }


    private ConfirmDecideFinishEvent buildConfirmCommand(ConfirmCommand confirmCommand, ConfirmExtCommand extCommand, String environment, DecideContextDTO decideContext, DecideResultDTO ruleDecide, NameListDecide nameListDecide) {
        ConfirmDecideFinishEvent event = ConfirmDecideFinishEvent.builder()
                .environment(environment)
                .bizCode(confirmCommand.getBizCode())
                .scenarioCode(confirmCommand.getScenarioCode())
                .requestNo(confirmCommand.getRequestNo())
                .params(confirmCommand.getParams())
                .actualStatus(confirmCommand.getStatus())
                .build();

        if (StringUtils.isNotBlank(decideContext.getOrderNo())) {
            event.setRequestAt(OrderNoUtils.getFormatedRequestAt(decideContext.getOrderNo()));
            event.setOrderNo(decideContext.getOrderNo());
        }
        event.setActualStatus(confirmCommand.getStatus());

        DecideLogDTO decideLog = new DecideLogDTO();
        decideLog.setSkipRuleEngine(decideContext.getSkipRuleEngine());
        decideLog.setSkipIndicatorQuery(decideContext.getSkipIndicatorQuery());
        decideLog.setSkipNameListEngine(decideContext.getSkipNameListEngine());
        decideLog.setIndicatorValues(decideContext.getIndicators());
        decideLog.setVariables(decideContext.getSystemParams());
        String ruleDecideType = null;
        if (null != ruleDecide) {
            decideLog.setHitRules(convertRules(ruleDecide.getHitRules()));
            decideLog.setFailedRules(convertRules(ruleDecide.getFailedRules()));
            decideLog.setFunctionVariables(ruleDecide.getFunctionVariables());
            ruleDecideType = ruleDecide.getDecideType();
        }

        if (null != nameListDecide) {
            decideLog.setHitNameList(NameListDTOConverter.INSTANCE.to(nameListDecide.getNameList()));
        }
        event.setDecideLog(decideLog);
        String decideType = null != decideContext.getFinalDecide() ? decideContext.getFinalDecide().getDecideType() : ruleDecideType;
        event.setDecideType(decideType);
        if (null != extCommand) {
            event.setPersistent(extCommand.getPersistent());
            event.setBatchNo(extCommand.getBatchNo());
        }
        event.setPolicyCode(decideContext.getPolicyCode());
        event.setPolicyVersion(decideContext.getPolicyVersion());
        return event;
    }

    private List<RuleExecuteItemDTO> convertRules(List<RuleExecuteItem> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return Collections.EMPTY_LIST;
        }
        List<RuleExecuteItemDTO> result = new ArrayList<>();
        for (RuleExecuteItem rule : rules) {
            result.add(RuleExecuteItemDTO.builder()
                    .ruleCode(rule.getRuleCode())
                    .ruleVersion(Long.valueOf(rule.getRuleVersion()))
                    .matchFailed(rule.getMatchFailed())
                    .enabled(rule.getEnabled())
                    .decideType(rule.getDecideType())
                    .build());

        }
        return result;
    }

    private DecideResultDTO finalDecide(NameListDecide nameListDecide, DecideResultDTO execute) {
        String decideType = nameListDecide.getDecideType();
        if (null != execute) {
            DecideType resultDecideType = EnumUtils.getEnum(DecideType.class, decideType);
            DecideType ruleDecideType = EnumUtils.getEnum(DecideType.class, execute.getDecideType());
            if (ruleDecideType.getPriority() > resultDecideType.getPriority()) {
                decideType = ruleDecideType.name();
            }
        }
        return DecideResultDTO.builder().decideType(decideType).build();
    }

    private List<ParamMappingDTO> queryRequestParamFromCache(String bizCode, String bizScenario, StageType type) {
        RequestParamKey requestParamKey = RequestParamKey.builder().bizCode(bizCode).scenarioCode(bizScenario).stageType(type.name()).build();
        return requestParamCache.get(requestParamKey);
    }

    private PolicyVersionDTO queryPolicyVersionFromCache(String bizCode, String bizScenario, StageType type, String environment) {
        PolicyVersionCacheKey cacheKey = PolicyVersionCacheKey.builder().bizCode(bizCode).scenarioCode(bizScenario).stageType(type.name()).environment(environment).build();
        return policyVersionRefreshableCache.get(cacheKey);
    }

    /**
     * 确认阶段参数校验
     *
     * @param params   请求参数
     * @param mappings 请求参数配置
     * @return 校验结果
     */
    public void validateConfirmParams(Map<String, Object> params, List<ParamMappingDTO> mappings) {

        if (MapUtil.isEmpty(params) && CollectionUtils.isNotEmpty(mappings)) {
            RiskguardExceptionEnum.PARAM_NECESSARY_VALIDATE_FAIL.assertIsTrue(false);
        }

        validateParams(params, mappings);
    }

    /**
     * 问询阶段参数校验
     *
     * @param params   请求参数
     * @param mappings 请求参数配置
     * @return 校验结果
     */
    public void validateParams(Map<String, Object> params, List<ParamMappingDTO> mappings) {
        log.info("参数校验，请求参数:{}", params);
        if (CollectionUtils.isEmpty(mappings)) {
            return;
        }

        Set<String> requiredParams = mappings.stream().filter(item -> Boolean.TRUE.equals(item.getRequired())).map(item -> item.getName()).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(requiredParams)) {
            Boolean validate = Boolean.TRUE;
            for (String requiredParam : requiredParams) {
                if (!requiredParam.contains(".")) {
                    validate = Objects.nonNull(params.get(requiredParam));
                }

                if (!checkPath(params, requiredParam)) {
                    validate = false;
                }
                RiskguardExceptionEnum.PARAM_NECESSARY_VALIDATE_FAIL.assertIsTrue(validate, requiredParam);
            }

        }
        for (ParamMappingDTO mapping : mappings) {
            String name = mapping.getName();

            String type = mapping.getType();
            log.info("问询，类型:{},必填参数:{}", type, requiredParams);
            if ("Object".equals(type) && CollectionUtils.isNotEmpty(requiredParams)) {
                List<String> list = ObjectFieldValidator.validateRequiredFields(params, requiredParams);
                if (CollectionUtils.isNotEmpty(list)) {
                    log.info("Object参数的类型不匹配, 参数: {}，期望类型: {} ", name, type);
                    RiskguardExceptionEnum.PARAM_TYPE_VALIDATE_FAIL.assertIsTrue(false, name, type);
                }
            } else {
                if (Objects.nonNull(params.get(name)) && !checkType(params.get(name), type)) {
                    log.info("参数的类型不匹配, 参数: {}，期望类型: {} ", name, type);
                    RiskguardExceptionEnum.PARAM_TYPE_VALIDATE_FAIL.assertIsTrue(false, name, type);
                }
            }
        }
    }

    private boolean checkPath(Map<String, Object> map, String path) {
        String[] parts = path.split("\\.");
        Object current = map;

        for (String part : parts) {
            if (current == null) {
                return false;
            }

            if (part.contains("[")) {
                String listKey = part.substring(0, part.indexOf('['));
                int index = Integer.parseInt(part.substring(part.indexOf('[') + 1, part.indexOf(']')));

                if (current instanceof Map) {
                    current = ((Map<?, ?>) current).get(listKey);
                } else {
                    return false;
                }

                if (current instanceof List) {
                    List<?> list = (List<?>) current;
                    if (index >= list.size()) {
                        return false;
                    }
                    current = list.get(index);
                } else {
                    return false;
                }
            } else {
                if (current instanceof Map) {
                    current = ((Map<?, ?>) current).get(part);
                } else {
                    return false;
                }
            }
        }

        return current != null;
    }


    private boolean checkType(Object value, String expectedType) {
        switch (expectedType) {
            case "String":
                return value instanceof String;
            case "Long":
                return value instanceof Long || value instanceof Integer;
            case "Double":
                return value instanceof Double;
            case "Boolean":
                return value instanceof Boolean;
            case "Date":
                return true;
            case "Decimal":
                return value instanceof Number;
            case "Enum":
                return true;
            case "List":
                return value instanceof List;
            case "Object":
                return value instanceof Object;
            default:
                log.info("未知类型: {}", expectedType);
                return true;
        }
    }

}
