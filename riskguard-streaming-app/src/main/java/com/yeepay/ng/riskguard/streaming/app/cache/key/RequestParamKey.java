/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.app.cache.key;

import lombok.*;

import java.io.Serializable;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/9 14:05
 */
/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/9 14:05
 */
@Setter
@Getter
@ToString
@EqualsAndHashCode
@Builder
public class RequestParamKey implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 业务版本编码
     */
    private String bizCode;

    /**
     * 业务场景编码
     */
    private String scenarioCode;

    /**
     * 业务阶段编码
     */
    private String stageType;


    /**
     * 环境编码
     */
    private String environment;
}