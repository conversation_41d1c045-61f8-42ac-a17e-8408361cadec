/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.app.cache;

import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.ng.riskguard.commons.cache.AbstractRefreshCache;
import com.yeepay.ng.riskguard.commons.cache.YopCacheLoader;
import com.yeepay.ng.riskguard.commons.valueobj.BizScenario;
import com.yeepay.ng.riskguard.streaming.app.cache.key.RequestParamKey;
import com.yeepay.ng.riskguard.streaming.domain.gateway.RequestParamGateway;
import com.yeepay.ng.riskguard.streaming.domain.gateway.backend.ParamMappingDTO;
import com.yeepay.ng.riskguard.streaming.domain.gateway.backend.RequestParamDTO;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * title: 风控策略配置本地缓存<br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/9 14:00
 */
@Component
@Slf4j
public class RequestParamRefreshableCache extends AbstractRefreshCache<RequestParamKey, List<ParamMappingDTO>> {

    @Setter(onMethod_ = @Autowired)
    private RequestParamGateway requestParamGateway;

    @Override
    public YopCacheLoader<RequestParamKey, List<ParamMappingDTO>> getCacheLoader() {
        return new YopCacheLoader<RequestParamKey, List<ParamMappingDTO>>() {
            @Override
            protected List<ParamMappingDTO> doLoad(RequestParamKey requestParamKey) throws Exception {
                log.info("load requestParam, cacheKey:{}", requestParamKey);
                BizScenario scenario = BizScenario.builder().bizCode(requestParamKey.getBizCode()).scenarioCode(requestParamKey.getScenarioCode()).build();
                List<RequestParamDTO> query = requestParamGateway.query(scenario, requestParamKey.getStageType());
                RequestParamDTO requestParamDTO = CollectionUtils.isNotEmpty(query) ? query.get(0) : null;
                Map<String, List<ParamMappingDTO>> result = Optional.ofNullable(requestParamDTO).map(item -> item.getItems()).orElse(new HashMap<>());
                return result.getOrDefault(requestParamKey.getStageType(), new ArrayList<>());
            }

            @Override
            protected String getCacheName() {
                return "RequestParamRefreshableCache";
            }
        };
    }

    @Override
    public long refreshAfterWrite() {
        // 缓存刷新时间为1小时
        return 60;
    }

    @Override
    public long maximumSize() {
        return 500;
    }
}