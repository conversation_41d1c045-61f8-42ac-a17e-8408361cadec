/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.app.cache;

import com.yeepay.ng.riskguard.commons.cache.AbstractRefreshCache;
import com.yeepay.ng.riskguard.commons.cache.YopCacheLoader;
import com.yeepay.ng.riskguard.commons.enums.PublishStatus;
import com.yeepay.ng.riskguard.commons.enums.StageType;
import com.yeepay.ng.riskguard.commons.utils.JobUtils;
import com.yeepay.ng.riskguard.rules.facade.indicator.dto.IndicatorDTO;
import com.yeepay.ng.riskguard.streaming.app.cache.key.IndicatorCacheKey;
import com.yeepay.ng.riskguard.streaming.app.cache.key.PolicyVersionCacheKey;
import com.yeepay.ng.riskguard.streaming.domain.gateway.IndicatorGateway;
import com.yeepay.ng.riskguard.streaming.domain.gateway.policy.PolicyVersionDTO;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * title: <br>
 * description: 指标本地缓存容器<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/25 14:38
 */
@Component
@Slf4j
public class IndicatorRefreshableCache extends AbstractRefreshCache<IndicatorCacheKey, List<IndicatorDTO>> {
    @Setter(onMethod_ = @Autowired)
    private IndicatorGateway indicatorGateway;

    @Override
    public YopCacheLoader<IndicatorCacheKey, List<IndicatorDTO>> getCacheLoader() {
        return new YopCacheLoader<IndicatorCacheKey, List<IndicatorDTO>>() {
            @Override
            protected List<IndicatorDTO> doLoad(IndicatorCacheKey indicatorCacheKey) throws Exception {
                log.info("load indicator cache key:{}", indicatorCacheKey);
                return indicatorGateway.queryIndicators(indicatorCacheKey.getBizCode(), indicatorCacheKey.getDataModelCode(), indicatorCacheKey.getStatus());
            }

            @Override
            protected String getCacheName() {
                return "IndicatorRefreshableCache";
            }
        };
    }

    @Override
    public long refreshAfterWrite() {
        // 缓存时间10分钟
        return 60 * 10 - 1;
    }

    @Override
    public long maximumSize() {
        return 100;
    }

    /**
     * 是否存在累计命中的规则列表的指标
     *
     * @param bizCode
     * @param dataModelCode
     * @param stage
     * @return
     */
    public boolean containsHitRulesAcc(String bizCode, String dataModelCode, String stage) {
        List<IndicatorDTO> indicatorDTOS = get(IndicatorCacheKey.builder()
                .bizCode(bizCode)
                .dataModelCode(dataModelCode)
                .status(PublishStatus.RUNNING.name())
                .build());
        if (indicatorDTOS == null || indicatorDTOS.isEmpty()) {
            return false;
        }
        return indicatorDTOS.stream().anyMatch(indicatorDTO -> indicatorDTO.getAccStage().equals(stage) && JobUtils.hitRules(indicatorDTO.getConfig().getAggregateField()));
    }

    /**
     * 包含与命中的规则列表无关的指标
     *
     * @param bizCode
     * @param dataModelCode
     * @param stage
     * @return
     */
    public boolean containsIndicators(String bizCode, String dataModelCode, String stage) {
        List<IndicatorDTO> indicatorDTOS = get(IndicatorCacheKey.builder()
                .bizCode(bizCode)
                .dataModelCode(dataModelCode)
                .status(PublishStatus.RUNNING.name())
                .build());
        if (indicatorDTOS == null || indicatorDTOS.isEmpty()) {
            return false;
        }
        List<IndicatorDTO> filtered = indicatorDTOS.stream().filter(indicatorDTO -> indicatorDTO.getAccStage().equals(stage)
                        && !JobUtils.hitRules(indicatorDTO.getConfig().getAggregateField()))
                .collect(Collectors.toList());
        return !filtered.isEmpty();
    }


    /**
     * 包含命中的规则编码作为维度的指标
     *
     * @param bizCode
     * @param dataModelCode
     * @param stage
     * @return
     */
    public boolean containsHitRuleCodeDimension(String bizCode, String dataModelCode, String stage) {
        List<IndicatorDTO> indicatorDTOS = get(IndicatorCacheKey.builder()
                .bizCode(bizCode)
                .dataModelCode(dataModelCode)
                .status(PublishStatus.RUNNING.name())
                .build());
        if (indicatorDTOS == null || indicatorDTOS.isEmpty()) {
            return false;
        }
        String hitCodeCodeField;
        if (StageType.CONFIRM.name().equals(stage)) {
            hitCodeCodeField = JobUtils.HIT_RULE_CODE_CONFIRM;
        } else {
            hitCodeCodeField = JobUtils.HIT_RULE_CODE_INQUIRY;
        }
        return indicatorDTOS.stream().anyMatch(indicatorDTO -> indicatorDTO.getAccStage().equals(stage) && (StringUtils.isNotEmpty(indicatorDTO.getDimensions()) && indicatorDTO.getDimensions().contains(hitCodeCodeField)));
    }
}