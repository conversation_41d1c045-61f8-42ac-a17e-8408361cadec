/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.app.service;

import com.yeepay.ng.riskguard.gateway.facade.event.ConfirmDecideFinishEvent;
import com.yeepay.ng.riskguard.gateway.facade.event.ConfirmEvent;
import com.yeepay.ng.riskguard.gateway.facade.event.DecideFinishEvent;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/4 15:04
 */
public interface SourceEventService {
    /**
     * 确认事件处理
     *
     * @param confirmEvent
     */
    void onConfirmEvent(ConfirmDecideFinishEvent confirmEvent);

    /**
     * 决策结束事件处理
     *
     * @param decideFinishEvent
     */
    void onDecideFinishEvent(DecideFinishEvent decideFinishEvent);
}