/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.app.service.impl;

import com.yeepay.ng.riskguard.commons.constants.Constants;
import com.yeepay.ng.riskguard.commons.enums.EnvironmentType;
import com.yeepay.ng.riskguard.commons.enums.OrderActualStatus;
import com.yeepay.ng.riskguard.commons.enums.StageType;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.commons.utils.JobUtils;
import com.yeepay.ng.riskguard.commons.utils.ParamsUtils;
import com.yeepay.ng.riskguard.engine.facade.rule.dto.data.RuleExecuteItemDTO;
import com.yeepay.ng.riskguard.gateway.facade.event.ConfirmDecideFinishEvent;
import com.yeepay.ng.riskguard.gateway.facade.event.DecideFinishEvent;
import com.yeepay.ng.riskguard.streaming.app.cache.IndicatorRefreshableCache;
import com.yeepay.ng.riskguard.streaming.app.service.SourceEventService;
import com.yeepay.ng.riskguard.streaming.domain.entity.DecideOrder;
import com.yeepay.ng.riskguard.streaming.domain.entity.DecideOrderQuery;
import com.yeepay.ng.riskguard.streaming.domain.entity.FlinkTable;
import com.yeepay.ng.riskguard.streaming.domain.gateway.DecideOrderGateway;
import com.yeepay.ng.riskguard.streaming.infrastructure.cache.TableSchemaCache;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/4 15:07
 */
@Component
@Slf4j
public class SourceEventServiceImpl implements SourceEventService {
    private static final JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    @Setter(onMethod_ = @Autowired)
    private Producer producer;

    @Setter(onMethod_ = @Autowired)
    private TableSchemaCache tableSchemaCache;

    @Setter(onMethod_ = @Autowired)
    private DecideOrderGateway decideOrderGateway;

    @Setter(onMethod_ = @Autowired)
    private RedisTemplate redisTemplate;

    @Setter(onMethod_ = @Autowired)
    private IndicatorRefreshableCache indicatorRefreshableCache;

    @Override
    public void onConfirmEvent(ConfirmDecideFinishEvent confirmEvent) {
        // 只有交易确认成功后数据才会进入flink
        if (OrderActualStatus.SUCCESS.name().equals(confirmEvent.getActualStatus())) {
            if (requestInSourceTopic(confirmEvent.getBizCode(), confirmEvent.getScenarioCode(), confirmEvent.getRequestNo(), StageType.CONFIRM.name())) {
                log.warn("confirm request already in source topic,bizCode:{},scenarioCode:{},requestNo:{}", confirmEvent.getBizCode(), confirmEvent.getScenarioCode(), confirmEvent.getRequestNo());
                return;
            }
            String sourceTableName = JobUtils.getSourceTableName(confirmEvent.getBizCode(), confirmEvent.getScenarioCode(), null, StageType.CONFIRM.name());
            FlinkTable flinkTable = tableSchemaCache.get(sourceTableName);
            if (null == flinkTable) {
                log.warn("flink table does not exist,tableName:{}, confirmEvent:{}", sourceTableName, confirmEvent);
                return;
            }
            DecideOrder decideOrder = decideOrderGateway.getDecideOrder(DecideOrderQuery.builder()
                    .bizCode(confirmEvent.getBizCode())
                    .scenarioCode(confirmEvent.getScenarioCode())
                    .requestNo(confirmEvent.getRequestNo())
                    .environment(EnvironmentType.PRODUCT.name())
                    .build());
            if (null == decideOrder) {
                // 延迟重投
                log.warn("decideOrder is null,requestNo:{}", confirmEvent.getRequestNo());
                return;
            }

            // 发送命中的规则数据至flink
            sendToHitRulesTopic(confirmEvent, StageType.CONFIRM.name());

            // 确认阶段如果不存在交易累计的指标则数据不发往flink
            if (!indicatorRefreshableCache.containsIndicators(confirmEvent.getBizCode(), confirmEvent.getScenarioCode(), StageType.CONFIRM.name())) {
                log.warn("confirm stage does not accumulate in indicator,bizCode:{},scenarioCode:{}", confirmEvent.getBizCode(), confirmEvent.getScenarioCode());
                return;
            }

            // 根据源表 元数据结构构造发到flink中的消息报文
            Map<String, Object> dataMap = new HashMap<>();
            Map<String, Object> params = new HashMap<>();
            params.putAll(decideOrder.getParams());
            params.putAll(confirmEvent.getParams());

            for (String field : flinkTable.getFields()) {
                if (JobUtils.REQUEST_NO.equals(field)) {
                    dataMap.put(field, confirmEvent.getRequestNo());
                } else {
                    dataMap.put(field, ParamsUtils.getPramValue(JobUtils.replaceDoubleDollarToDot(field), params));
                }
            }
            dataMap.put("bizCode", confirmEvent.getBizCode());
            dataMap.put("scenarioCode", confirmEvent.getScenarioCode());
            dataMap.put(JobUtils.DECIDE_TYPE_INQUIRY, decideOrder.getDecideType());
            dataMap.put(JobUtils.DECIDE_TYPE_CONFIRM, confirmEvent.getDecideType());
            addHitRulesToMapData(dataMap, confirmEvent);


            // 发送数据到flink kafka connector
            String topic = JobUtils.getSourceTopic(confirmEvent.getBizCode(), confirmEvent.getScenarioCode(), StageType.CONFIRM.name());
            log.info("send confirm to topic:{}", topic);
            ProducerRecord producerRecord = new ProducerRecord(topic, confirmEvent.getRequestNo(), JSON_PARSER.toJson(dataMap));
            producer.send(producerRecord);
        }
    }

    @Override
    public void onDecideFinishEvent(DecideFinishEvent decideFinishEvent) {
        if (EnvironmentType.ACCOMPANY.name().equals(decideFinishEvent.getEnvironment())) {
            log.info("request with accompany environment does not accumulate in indicator");
            return;
        }
        // 发送命中的规则数据至flink
        sendToHitRulesTopic(decideFinishEvent, StageType.INQUIRY.name());

        if (requestInSourceTopic(decideFinishEvent.getBizCode(), decideFinishEvent.getScenarioCode(), decideFinishEvent.getRequestNo(), StageType.INQUIRY.name())) {
            log.warn("decide request already in source topic,bizCode:{},scenarioCode:{},requestNo:{}", decideFinishEvent.getBizCode(), decideFinishEvent.getScenarioCode(), decideFinishEvent.getRequestNo());
            return;
        }
        // 发送问询阶段累计的数据至flink
        sendToAccTopic(decideFinishEvent, StageType.INQUIRY.name());
    }

    private void addHitRulesToMapData(Map<String, Object> dataMap, DecideFinishEvent decideFinishEvent) {
        if (null == decideFinishEvent.getDecideLog()) {
            return;
        }
        List<String> hitRules = new ArrayList<>();
        // 将命中的策略规则编码加到source数据中
        if (CollectionUtils.isNotEmpty(decideFinishEvent.getDecideLog().getHitRules())) {
            for (RuleExecuteItemDTO rule : decideFinishEvent.getDecideLog().getHitRules()) {
                hitRules.add(rule.getRuleCode());
            }
        }

        // 将命中的名单规则编码加到source数据中
        if (CollectionUtils.isNotEmpty(decideFinishEvent.getDecideLog().getHitNameListRules())) {
            for (RuleExecuteItemDTO nameListRule : decideFinishEvent.getDecideLog().getHitNameListRules()) {
                hitRules.add(nameListRule.getRuleCode());
            }
        }

        if (CollectionUtils.isNotEmpty(hitRules)) {
            dataMap.put("hitRules", hitRules);
        }
    }


    private void sendToHitRulesTopic(DecideFinishEvent decideFinishEvent, String stage) {
        // 有决策信息才发到flink中
        if (null == decideFinishEvent.getDecideLog()) {
            log.warn("decideLog is null");
            return;
        }
        String hitsRulesTable = JobUtils.hitsRulesTableName(decideFinishEvent.getBizCode());
        FlinkTable flinkTable = tableSchemaCache.get(hitsRulesTable);
        if (null == flinkTable) {
            log.warn("hitsRulesTable {} not exists", hitsRulesTable);
            return;
        }
        if (CollectionUtils.isEmpty(decideFinishEvent.getDecideLog().getHitRules())) {
            log.warn("hitRules is empty");
            return;
        }

        // 不存在运行中的指标累计命中的规则列表的指标就不往flink发数据
        if (!indicatorRefreshableCache.containsHitRulesAcc(decideFinishEvent.getBizCode(), decideFinishEvent.getScenarioCode(), stage)) {
            log.warn("indicator refreshable cache does not contain hitRulesAcc,bizCode:{},scenarioCode:{}", decideFinishEvent.getBizCode(), decideFinishEvent.getScenarioCode());
            return;
        }

        List<RuleExecuteItemDTO> matchedRules = decideFinishEvent.getDecideLog().getHitRules();
        List<String> hitRules = new ArrayList<>();
        for (RuleExecuteItemDTO rule : matchedRules) {
            hitRules.add(rule.getRuleCode());
        }
        decideFinishEvent.getParams().put("hitRules", hitRules);
        send(flinkTable, decideFinishEvent, JobUtils.hitsRulesTopicName(decideFinishEvent.getBizCode()));
    }

    private void sendToAccTopic(DecideFinishEvent decideFinishEvent, String stage) {
        String sourceTableName = JobUtils.getSourceTableName(decideFinishEvent.getBizCode(), decideFinishEvent.getScenarioCode(), null, stage);
        FlinkTable flinkTable = tableSchemaCache.get(sourceTableName);
        if (null == flinkTable) {
            log.warn("flink table does not exist,tableName:{}", sourceTableName);
            return;
        }
        // 不存在运行中的指标累计的指标就不往flink发数据
        if (!indicatorRefreshableCache.containsIndicators(decideFinishEvent.getBizCode(), decideFinishEvent.getScenarioCode(), StageType.INQUIRY.name())) {
            log.warn("indicator refreshable cache does not contain indicators,bizCode:{},scenarioCode:{}", decideFinishEvent.getBizCode(), decideFinishEvent.getScenarioCode());
            return;
        }
        send(flinkTable, decideFinishEvent, JobUtils.getSourceTopic(decideFinishEvent.getBizCode(), decideFinishEvent.getScenarioCode(), StageType.INQUIRY.name()));
    }

    private void send(FlinkTable flinkTable, DecideFinishEvent decideFinishEvent, String sourceTopic) {
        Map<String, Object> dataMap = new HashMap<>();
        for (String field : flinkTable.getFields()) {
            dataMap.put(field, ParamsUtils.getPramValue(JobUtils.replaceDoubleDollarToDot(field), decideFinishEvent.getParams()));
        }
        dataMap.put("bizCode", decideFinishEvent.getBizCode());
        dataMap.put("scenarioCode", decideFinishEvent.getScenarioCode());
        dataMap.put("orderNo", decideFinishEvent.getOrderNo());
        dataMap.put("requestNo", decideFinishEvent.getRequestNo());
        dataMap.put(JobUtils.REQUEST_AT, decideFinishEvent.getRequestAt());

        dataMap.put(JobUtils.DECIDE_TYPE_INQUIRY, decideFinishEvent.getDecideType());
        addHitRulesToMapData(dataMap, decideFinishEvent);
        // 发送数据到flink kafka connector

        log.info("send data to topic:{}", sourceTopic);
        ProducerRecord producerRecord = new ProducerRecord(sourceTopic, decideFinishEvent.getRequestNo(), JSON_PARSER.toJson(dataMap));
        producer.send(producerRecord);
    }

    /**
     * 请求是否已经进入过源表了
     *
     * @param bizCode
     * @param scenarioCode
     * @param requestNo
     * @param stage
     * @return
     */
    private boolean requestInSourceTopic(String bizCode, String scenarioCode, String requestNo, String stage) {
        String uniqueKey = bizCode + Constants.COLON + scenarioCode + Constants.COLON + requestNo + Constants.COLON + stage;
        try {
            return !redisTemplate.opsForValue().setIfAbsent(uniqueKey, 1, 7, TimeUnit.DAYS);
        } catch (Exception e) {
            // 异常兜底，产生异常时不影响数据进入源表
            log.warn("redis setIfAbsent error,uniqueKey:{}", uniqueKey);
            return false;
        }
    }
}