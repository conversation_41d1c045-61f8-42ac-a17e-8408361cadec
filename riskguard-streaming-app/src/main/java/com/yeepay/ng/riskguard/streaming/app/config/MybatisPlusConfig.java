/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.app.config;

import com.baomidou.dynamic.datasource.creator.DataSourceCreator;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.yeepay.g3.utils.common.datasource.DataSourceFactoryBean;
import com.yeepay.g3.utils.common.datasource.impl.DruidPooledDataSource;
import com.yeepay.g3.utils.common.datasource.impl.DruidPooledDataSourceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.plugin.Interceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.List;

import static com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration.DRUID_ORDER;
import static com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration.JNDI_ORDER;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/13 11:34 上午
 */
@Configuration
@Slf4j
@MapperScan(basePackages = {
        "com.yeepay.ng.riskguard.streaming.infrastructure.mapper"
})
public class MybatisPlusConfig {
    @Bean
    @Order((JNDI_ORDER + DRUID_ORDER) / 2)
    public DataSourceCreator localDruidDataSource() {
        return new DataSourceCreator() {
            @Override
            public DataSource createDataSource(DataSourceProperty dataSourceProperty) {
                try {
                    final DataSourceFactoryBean factoryBean = new DataSourceFactoryBean();
                    factoryBean.setName(dataSourceProperty.getPoolName());
                    factoryBean.setPooledDataSourceFactory(new DruidPooledDataSourceFactory());
                    return (DataSource) factoryBean.getObject();
                } catch (Exception e) {
                    log.warn("fail to createDataSource, props:{}", dataSourceProperty, e);
                }
                return null;
            }

            @Override
            public boolean support(DataSourceProperty dataSourceProperty) {
                return dataSourceProperty.getType() == DruidPooledDataSource.class;
            }
        };
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptors = new MybatisPlusInterceptor();
        interceptors.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        interceptors.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptors;
    }

    @Bean
    public MybatisSqlSessionFactoryBean getSqlSessionFactoryBean(DataSource dataSource, List<Interceptor> plugins) throws Exception {
        final MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        PathMatchingResourcePatternResolver pathMatchingResourcePatternResolver = new PathMatchingResourcePatternResolver();
        sqlSessionFactoryBean.setMapperLocations(pathMatchingResourcePatternResolver.getResources("classpath*:/mapper/*Mapper.xml"));
        sqlSessionFactoryBean.setFailFast(true);
        if (CollectionUtils.isNotEmpty(plugins)) {
            sqlSessionFactoryBean.setPlugins(plugins.toArray(new Interceptor[plugins.size()]));
        }
        return sqlSessionFactoryBean;
    }

    @Bean
    public JdbcTemplate myJdbcTemplate(DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}
