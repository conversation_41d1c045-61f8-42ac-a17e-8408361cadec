/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.app.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.backoff.FixedBackOff;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * title: kafka客户端bean注册<br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/23 20:33
 */
@Configuration
@Slf4j
@EnableKafka
public class KafkaConfig {
    private static final Properties props = new Properties();

    static {
        try {
            InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream("runtimecfg/" + "kafka.properties");

            props.load(is);
        } catch (Exception e) {
            log.error("error happened when load kafka host", e);
        }

    }

    @Bean
    public ConsumerFactory<String, String> consumerFactory() {
        Map<String, Object> props = new HashMap<>();
        props.put(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                KafkaConfig.props.getProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG));
        props.put(
                ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG,
                StringDeserializer.class);
        props.put(
                ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,
                StringDeserializer.class);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, StringUtils.isNotEmpty(KafkaConfig.props.getProperty(ConsumerConfig.MAX_POLL_RECORDS_CONFIG))?Integer.parseInt(KafkaConfig.props.getProperty(ConsumerConfig.MAX_POLL_RECORDS_CONFIG)):500); // 每次poll最多1000条
        props.put(ConsumerConfig.FETCH_MAX_BYTES_CONFIG, 50 * 1024 * 1024); // 单次拉取最大50MB
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 10 * 1024 * 1024); // 每个分区10MB
        props.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 500); // 等待超时500ms
        return new DefaultKafkaConsumerFactory<>(props);
    }

    /**
     * flink源数据处理线程池
     * @return
     */
    @Bean("riskguardEventContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String>
    kafkaListenerContainerFactory() {

        ConcurrentKafkaListenerContainerFactory<String, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setQueueCapacity(1000);
        String coreThreads = props.getProperty("consumer.core.threads");
        if (StringUtils.isNotEmpty(coreThreads)) {
            executor.setCorePoolSize(Integer.parseInt(coreThreads));
        }

        String maxThreads = props.getProperty("consumer.max.threads");
        if (StringUtils.isNotEmpty(maxThreads)) {
            executor.setMaxPoolSize(Integer.parseInt(maxThreads));
        }

        String queueCapacity = props.getProperty("consumer.queue.capacity");
        if (StringUtils.isNotEmpty(queueCapacity)) {
            executor.setQueueCapacity(Integer.parseInt(queueCapacity));
        }
        executor.setRejectedExecutionHandler((r, executor1) -> log.warn("riskguard-event-consumer thread pool rejected"));
        executor.setThreadNamePrefix("riskguard-event-consumer-");
        executor.initialize();
        // 消费线程池
        factory.getContainerProperties().setConsumerTaskExecutor(executor);
        // 设置重试次数
        DefaultErrorHandler errorHandler = new DefaultErrorHandler(
                (record, exception) -> {
                    log.error("Failed to process message after all retries: {}", record, exception);
                },
                new FixedBackOff(1000L, 5L)  // 间隔1秒，重试5次
        );
        factory.setCommonErrorHandler(errorHandler);
        // 设置手动ACK模式
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);

        return factory;
    }

    @Bean("riskguardAccompanyEventContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> riskguardAccompanyEventContainerFactory() {

        ConcurrentKafkaListenerContainerFactory<String, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setQueueCapacity(1000);
        String coreThreads = props.getProperty("accompany.consumer.core.threads");
        if (StringUtils.isNotEmpty(coreThreads)) {
            executor.setCorePoolSize(Integer.parseInt(coreThreads));
        }

        String maxThreads = props.getProperty("accompany.consumer.max.threads");
        if (StringUtils.isNotEmpty(maxThreads)) {
            executor.setMaxPoolSize(Integer.parseInt(maxThreads));
        }

        String queueCapacity = props.getProperty("accompany.consumer.queue.capacity");
        if (StringUtils.isNotEmpty(queueCapacity)) {
            executor.setQueueCapacity(Integer.parseInt(queueCapacity));
        }
        executor.setRejectedExecutionHandler((r, executor1) -> log.warn("riskguard-event-consumer thread pool rejected"));
        executor.setThreadNamePrefix("riskguard-event-consumer-");
        executor.initialize();
        // 消费线程池
        factory.getContainerProperties().setConsumerTaskExecutor(executor);
        // 设置重试次数
        DefaultErrorHandler errorHandler = new DefaultErrorHandler(
                (record, exception) -> {
                    log.error("Failed to process message after all retries: {}", record, exception);
                },
                new FixedBackOff(1000L, 5L)  // 间隔1秒，重试5次
        );
        factory.setCommonErrorHandler(errorHandler);
        // 设置手动ACK模式
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);

        return factory;
    }

    @Bean("riskguardPersistenceEventContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> riskguardPersistenceEventContainerFactory() {

        ConcurrentKafkaListenerContainerFactory<String, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setQueueCapacity(1000);
        String coreThreads = props.getProperty("persistence.consumer.core.threads");
        if (StringUtils.isNotEmpty(coreThreads)) {
            executor.setCorePoolSize(Integer.parseInt(coreThreads));
        }

        String maxThreads = props.getProperty("persistence.consumer.max.threads");
        if (StringUtils.isNotEmpty(maxThreads)) {
            executor.setMaxPoolSize(Integer.parseInt(maxThreads));
        }

        String queueCapacity = props.getProperty("persistence.consumer.queue.capacity");
        if (StringUtils.isNotEmpty(queueCapacity)) {
            executor.setQueueCapacity(Integer.parseInt(queueCapacity));
        }
        executor.setRejectedExecutionHandler((r, executor1) -> log.warn("riskguard-event-consumer thread pool rejected"));
        executor.setThreadNamePrefix("riskguard-event-consumer-");
        executor.initialize();
        // 消费线程池
        factory.getContainerProperties().setConsumerTaskExecutor(executor);
        // 设置重试次数
        DefaultErrorHandler errorHandler = new DefaultErrorHandler(
                (record, exception) -> {
                    log.error("Failed to process message after all retries: {}", record, exception);
                },
                new FixedBackOff(1000L, 5L)  // 间隔1秒，重试5次
        );
        factory.setCommonErrorHandler(errorHandler);
        // 设置手动ACK模式
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);

        return factory;
    }

    @Bean("riskguardChecklistEventContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> riskguardChecklistEventContainerFactory() {

        ConcurrentKafkaListenerContainerFactory<String, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setQueueCapacity(1000);
        String coreThreads = props.getProperty("checklist.consumer.core.threads");
        if (StringUtils.isNotEmpty(coreThreads)) {
            executor.setCorePoolSize(Integer.parseInt(coreThreads));
        }

        String maxThreads = props.getProperty("checklist.consumer.max.threads");
        if (StringUtils.isNotEmpty(maxThreads)) {
            executor.setMaxPoolSize(Integer.parseInt(maxThreads));
        }

        String queueCapacity = props.getProperty("checklist.consumer.queue.capacity");
        if (StringUtils.isNotEmpty(queueCapacity)) {
            executor.setQueueCapacity(Integer.parseInt(queueCapacity));
        }
        executor.setRejectedExecutionHandler((r, executor1) -> log.warn("riskguard-event-consumer thread pool rejected"));
        executor.setThreadNamePrefix("riskguard-event-consumer-");
        executor.initialize();
        // 消费线程池
        factory.getContainerProperties().setConsumerTaskExecutor(executor);
        // 设置重试次数
        DefaultErrorHandler errorHandler = new DefaultErrorHandler(
                (record, exception) -> {
                    log.error("Failed to process message after all retries: {}", record, exception);
                },
                new FixedBackOff(1000L, 5L)  // 间隔1秒，重试5次
        );
        factory.setCommonErrorHandler(errorHandler);
        // 设置手动ACK模式
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);

        return factory;
    }

    @Bean("riskguardOrchestrationEventContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> riskguardOrchestrationEventContainerFactory() {

        ConcurrentKafkaListenerContainerFactory<String, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setQueueCapacity(1000);
        String coreThreads = props.getProperty("orchestration.consumer.core.threads");
        if (StringUtils.isNotEmpty(coreThreads)) {
            executor.setCorePoolSize(Integer.parseInt(coreThreads));
        }

        String maxThreads = props.getProperty("orchestration.consumer.max.threads");
        if (StringUtils.isNotEmpty(maxThreads)) {
            executor.setMaxPoolSize(Integer.parseInt(maxThreads));
        }

        String queueCapacity = props.getProperty("orchestration.consumer.queue.capacity");
        if (StringUtils.isNotEmpty(queueCapacity)) {
            executor.setQueueCapacity(Integer.parseInt(queueCapacity));
        }
        executor.setRejectedExecutionHandler((r, executor1) -> log.warn("riskguard-event-consumer thread pool rejected"));
        executor.setThreadNamePrefix("riskguard-event-consumer-");
        executor.initialize();
        // 消费线程池
        factory.getContainerProperties().setConsumerTaskExecutor(executor);
        // 设置重试次数
        DefaultErrorHandler errorHandler = new DefaultErrorHandler(
                (record, exception) -> {
                    log.error("Failed to process message after all retries: {}", record, exception);
                },
                new FixedBackOff(1000L, 5L)  // 间隔1秒，重试5次
        );
        factory.setCommonErrorHandler(errorHandler);
        // 设置手动ACK模式
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);

        return factory;
    }

    @Bean
    public Producer<String, String> producer() {
        Properties properties = new Properties();
        properties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, props.getProperty(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG));
        properties.put(ProducerConfig.ACKS_CONFIG, "all");
        properties.put(ProducerConfig.RETRIES_CONFIG, 0);
        properties.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        properties.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        properties.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);
        properties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.StringSerializer");
        properties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.StringSerializer");

        Producer<String, String> producer = new KafkaProducer<>(properties);
        return producer;
    }
}
