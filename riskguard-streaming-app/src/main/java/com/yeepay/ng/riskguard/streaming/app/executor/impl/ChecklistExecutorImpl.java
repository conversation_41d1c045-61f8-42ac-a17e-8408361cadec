package com.yeepay.ng.riskguard.streaming.app.executor.impl;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.ng.riskguard.commons.enums.NotifyStatus;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.commons.utils.OrderNoUtils;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideCommand;
import com.yeepay.ng.riskguard.gateway.facade.dto.notify.NotifyCommand;
import com.yeepay.ng.riskguard.gateway.facade.event.DecideFinishEvent;
import com.yeepay.ng.riskguard.streaming.app.executor.ChecklistExecutor;
import com.yeepay.ng.riskguard.streaming.domain.config.ConfigEnum;
import com.yeepay.ng.riskguard.streaming.domain.entity.Order;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderControlChecklist;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderQueryReqest;
import com.yeepay.ng.riskguard.streaming.domain.entity.OrderUpdateReqest;
import com.yeepay.ng.riskguard.streaming.domain.gateway.NotifyGateway;
import com.yeepay.ng.riskguard.streaming.domain.model.ChecklistMessage;
import com.yeepay.ng.riskguard.streaming.domain.notify.ChecklistNotify;
import com.yeepay.ng.riskguard.streaming.domain.service.OrderControlChecklistService;
import com.yeepay.ng.riskguard.streaming.domain.service.OrderService;
import com.yeepay.ng.riskguard.streaming.domain.service.RiskNotifyService;
import com.yeepay.ng.riskguard.streaming.domain.service.PolicyOrchestrationService;
import com.yeepay.ng.riskguard.streaming.domain.entity.PolicyOrchestrationList;
import com.yeepay.ng.riskguard.streaming.domain.entity.PolicyOrchestrationRequest;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.yeepay.ng.riskguard.commons.enums.StageType;
import com.yeepay.ng.riskguard.streaming.domain.entity.RiskNotify;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/22 11:34
 */

@Slf4j
@Component
public class ChecklistExecutorImpl implements ChecklistExecutor {

    private static final JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    private static final JsonMapper JSON_MAPPER = JsonMapper.nonNullMapper();

    @Setter(onMethod_ = @Autowired)
    private OrderControlChecklistService checklistService;

    @Setter(onMethod_ = @Autowired)
    private OrderService orderService;

    @Setter(onMethod_ = @Autowired)
    private NotifyGateway notifyGateway;

    @Setter(onMethod_ = @Autowired)
    private RiskNotifyService riskNotifyService;

    @Setter(onMethod_ = @Autowired)
    private PolicyOrchestrationService policyOrchestrationService;

    @Override
    public void report(ChecklistMessage checklistMessage) {
        try {
            Map<String, String> decideTypeMapping = ConfigUtils.getSysConfigParam(ConfigEnum.RISKGUARD_DECIDE_TYPE_MAPPING, Map.class);
            String checklistResult = decideTypeMapping.get(checklistMessage.getHandleResult());
            if (StringUtils.isEmpty(checklistResult)) {
                log.warn("report checklist result illegal character, handleResult:{}", checklistMessage.getHandleResult());
                return;
            }
            if(StringUtils.isEmpty(checklistMessage.getStage())){
                checklistMessage.setStage(StageType.INQUIRY.name());
            }

            Order order = null;
            if (StringUtils.isNotBlank(checklistMessage.getRequestNo())) {
                OrderQueryReqest orderQueryReqest = new OrderQueryReqest();
                orderQueryReqest.setRequestNo(checklistMessage.getRequestNo());
                orderQueryReqest.setStage(checklistMessage.getStage());
                order = orderService.find(orderQueryReqest);
            }

            if(order == null){
                log.warn("report order is null, requestNo:{}", checklistMessage.getRequestNo());
                return;
            }

            OrderUpdateReqest reqest = OrderUpdateReqest.builder()
                    .stage(checklistMessage.getStage())
                    .bizCode(checklistMessage.getBizCode())
//                    .scenarioCode(checklistMessage.getScenarioCode())
                    .requestNo(checklistMessage.getRequestNo())
//                    .orderNo(checklistMessage.getOrderNo())
                    .checklistResult(checklistResult)
                    .build();
            orderService.updateChecklistResult(reqest);


            //如果有核查单信息则记录核查详情
            if (StringUtils.isNotBlank(checklistMessage.getChecklistNo())) {
                OrderControlChecklist orderControlChecklist = OrderControlChecklist.builder()
                        .stage(checklistMessage.getStage())
                        .bizCode(checklistMessage.getBizCode())
                        .scenarioCode(checklistMessage.getScenarioCode())
                        .requestNo(checklistMessage.getRequestNo())
                        .orderNo(checklistMessage.getOrderNo())
                        .checklistResult(checklistResult)
                        .checklistNo(checklistMessage.getChecklistNo())
                        .investigationInfo(checklistMessage.getInvestigationInfo())
                        .relatedOrderList(checklistMessage.getRelatedOrderList())
                        .build();
                if (order != null) {
                    orderControlChecklist.setRequestAt(order.getRequestAt());
                    orderControlChecklist.setOrderNo(order.getOrderNo());
                    orderControlChecklist.setScenarioCode(order.getScenarioCode());
                } else if (StringUtils.isNotBlank(checklistMessage.getOrderNo())) {
                    orderControlChecklist.setRequestAt(OrderNoUtils.parseRequestAt(checklistMessage.getOrderNo().substring(0, 14)));
                }
                checklistService.create(orderControlChecklist);
            }

            // 查询核查结果阶段的场景编排配置
            triggerOrchestrationIfConfigured(checklistMessage, order);

            //给业务通知核查单结果
            notifyCustomers(checklistMessage);
        } catch (Exception e) {
            log.error("ChecklistExecutor report error. message:{}", JSON_PARSER.toJson(checklistMessage), e);
            throw new RuntimeException("ChecklistExecutor report error");
        }

    }

    private void notifyCustomers(ChecklistMessage checklistMessage) {
        ChecklistNotify notify = new ChecklistNotify();
        notify.setRequestNo(checklistMessage.getRequestNo())
                .setBizCode(checklistMessage.getBizCode())
//                .setScenarioCode(checklistMessage.getScenarioCode())
//                .setOrderNo(checklistMessage.getOrderNo())
                .setHandleResult(checklistMessage.getHandleResult())
                .setInvestigationInfo(checklistMessage.getInvestigationInfo())
                .setRelatedOrderList(checklistMessage.getRelatedOrderList())
                .setChecklistNo(checklistMessage.getChecklistNo());

        RiskNotify detail = riskNotifyService.detail(checklistMessage.getBizCode(), checklistMessage.getScenarioCode(), StageType.INQUIRY.name(), checklistMessage.getRequestNo());
        if (detail == null) {
            log.error("notifyCustomers notify is null, notify:{}", JSON_MAPPER.toJson(notify));
            return;
        }
        log.info("notify info, info:{}", JSON_MAPPER.toJson(notify));
        Boolean notifyResult = notifyGateway.notify(detail.getNotifyUrl(), JSON_MAPPER.toJson(notify));
        log.info("notify info result , notifyResult:{}", notifyResult);
        int notifyStatus = BooleanUtils.isTrue(notifyResult) ? NotifyStatus.SUCCESS.getValue() : NotifyStatus.FAILED.getValue();
        if (null != detail) {
            detail.setChecklistNotifyStatus(notifyStatus);
            detail.setChecklistNotifyTimes(1);
            riskNotifyService.update(detail);
        }
    }

    /**
     * 查询核查结果阶段的场景编排配置，判断是否配置场景编排
     * 如果配置了场景编排，则触发场景编排；如果没有配置，则直接回调通知
     */
    private void triggerOrchestrationIfConfigured(ChecklistMessage checklistMessage, Order order) {
        try {
            log.info("开始查询核查结果阶段的场景编排配置, requestNo: {}, policyCode: {}, policyVersion: {}",
                    checklistMessage.getRequestNo(), order.getPolicyCode(), order.getPolicyVersion());

            // 查询策略编排配置
            PolicyOrchestrationRequest orchestrationRequest = PolicyOrchestrationRequest.builder()
                    .policyCode(order.getPolicyCode())
                    .policyVersion(order.getPolicyVersion())
                    .build();

            List<PolicyOrchestrationList> orchestrationHandlers = policyOrchestrationService.find(orchestrationRequest);

            if (orchestrationHandlers == null || orchestrationHandlers.isEmpty()) {
                log.info("未找到核查结果阶段的场景编排配置, requestNo: {}, policyCode: {}, policyVersion: {}",
                        checklistMessage.getRequestNo(), order.getPolicyCode(), order.getPolicyVersion());
                return;
            }

            log.info("找到 {} 个场景编排配置, requestNo: {}", orchestrationHandlers.size(), checklistMessage.getRequestNo());

            // 遍历处理每个编排配置
            for (PolicyOrchestrationList handler : orchestrationHandlers) {
                processChecklistOrchestrationHandler(handler, checklistMessage, order);
            }

        } catch (Exception e) {
            log.error("查询或处理核查结果场景编排配置时发生错误, requestNo: {}", checklistMessage.getRequestNo(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 处理单个场景编排配置
     */
    private void processChecklistOrchestrationHandler(PolicyOrchestrationList handler, ChecklistMessage checklistMessage, Order order) {
        try {
            log.info("处理场景编排配置: handlerCode={}, bizCode={}, scenarioCode={}, requestNo={}",
                    handler.getCode(), handler.getBizCode(), handler.getScenarioCode(), checklistMessage.getRequestNo());

            // 检查编排配置是否启用
            if (handler.getEnabled() == null || handler.getEnabled() != 1) {
                log.info("场景编排配置未启用, handlerCode={}, requestNo={}", handler.getCode(), checklistMessage.getRequestNo());
                return;
            }

            // TODO: 这里可以添加SPEL表达式匹配逻辑，类似OrchestrationEventListener中的实现
            // 目前先简单处理，后续可以根据需要添加更复杂的匹配规则

            log.info("场景编排配置匹配成功，开始执行编排逻辑, handlerCode={}, requestNo={}",
                    handler.getCode(), checklistMessage.getRequestNo());

            // 根据执行方式处理
            if ("SYNC".equals(handler.getExecuteMethod())) {
                log.info("执行同步场景编排, handlerCode={}, requestNo={}", handler.getCode(), checklistMessage.getRequestNo());
                // 同步执行逻辑 - 目前暂不实现，可以后续扩展
            } else if ("ASYNC".equals(handler.getExecuteMethod())) {
                log.info("执行异步场景编排, handlerCode={}, requestNo={}", handler.getCode(), checklistMessage.getRequestNo());
                // 异步执行逻辑 - 目前暂不实现，可以后续扩展
            } else {
                log.warn("不支持的执行方式: {}, handlerCode={}, requestNo={}",
                        handler.getExecuteMethod(), handler.getCode(), checklistMessage.getRequestNo());
            }

        } catch (Exception e) {
            log.error("处理场景编排配置时发生错误, handlerCode={}, requestNo={}",
                    handler.getCode(), checklistMessage.getRequestNo(), e);
        }
    }
}
