package com.yeepay.ng.riskguard.streaming.app.service.impl;

import com.yeepay.ng.riskguard.commons.aspect.StopWatchTime;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.gateway.facade.event.DecideFinishEvent;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * title: 陪跑事件发送器
 * description:
 * Copyright: Copyright (c)2024
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/3/14 10:40
 */
@Component
@Slf4j
public class AccompanySender {

    @Setter(onMethod_ = @Autowired)
    private Producer producer;

    private final static JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    /**
     * 发送异步陪跑事件
     *
     * @param event
     */
    @StopWatchTime
    public void send(DecideFinishEvent event) {
        String body = null;
        try {
            body = JSON_PARSER.toJson(event);
            ProducerRecord producerRecord = new ProducerRecord("riskguard_accompany_event", event.getRequestNo(), body);
            producerRecord.headers().add("event-type", "decide".getBytes(StandardCharsets.UTF_8));
            producer.send(producerRecord, (metadata, exception) -> {
                if (exception != null) {
                    log.error("send accompany event failed", exception);
                }
                log.info("send accompany event, requestNo:{},  decideCommand:{}", event.getRequestNo(), JSON_PARSER.toJson(event));
            });
        } catch (Exception e) {
            log.error("error happened when send accompany finish event, body:{}", body, e);
        }
    }

}
