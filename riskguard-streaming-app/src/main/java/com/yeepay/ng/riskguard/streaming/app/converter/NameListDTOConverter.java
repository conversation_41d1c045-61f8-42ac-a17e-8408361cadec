/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.app.converter;


import com.yeepay.ng.riskguard.engine.facade.namelist.dto.NameListDTO;
import com.yeepay.ng.riskguard.streaming.domain.entity.NameList;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.Collections;
import java.util.List;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/1/9 13:23
 */
@Mapper(
        imports = {StringUtils.class,
                Collections.class}
)
public interface NameListDTOConverter {
    NameListDTOConverter INSTANCE = Mappers.getMapper(NameListDTOConverter.class);

    @Mappings({
            @Mapping(target = "effectiveScope", expression = "java(StringUtils.isEmpty(nameList.getEffectiveScope())?null:Collections.singletonList(nameList.getEffectiveScope()))")
    })
    NameListDTO to(NameList nameList);

    List<NameListDTO> to(List<NameList> nameList);
}