/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.app.service.impl;

import com.yeepay.ng.riskguard.commons.aspect.StopWatchTime;
import com.yeepay.ng.riskguard.commons.json.JsonParser;
import com.yeepay.ng.riskguard.commons.json.JsonParserFactory;
import com.yeepay.ng.riskguard.gateway.facade.dto.confirm.ConfirmCommand;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideCommand;
import com.yeepay.ng.riskguard.gateway.facade.event.ConfirmDecideFinishEvent;
import com.yeepay.ng.riskguard.gateway.facade.event.DecideFinishEvent;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * title: 事件发送器<br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/2 17:54
 */
@Component
@Slf4j
public class DecideSender {

    @Setter(onMethod_ = @Autowired)
    private Producer producer;

    private final static JsonParser JSON_PARSER = JsonParserFactory.getJsonParser();

    @Value("${riskguard.async.scene.topic}")
    private String topic;
    /**
     * 发送异步商户巡检问询
     * @param decideCommand
     */
    @StopWatchTime
    public void sendDecideEvent(DecideFinishEvent decideCommand) {
        String body = null;
        try {
            body = JSON_PARSER.toJson(decideCommand);
            ProducerRecord producerRecord = new ProducerRecord(topic, decideCommand.getRequestNo(), body);
            producerRecord.headers().add("event-type", "decide".getBytes(StandardCharsets.UTF_8));
            producer.send(producerRecord, (metadata, exception) -> {
                if (exception != null) {
                    log.error("send decide event failed", exception);
                }
                log.info("send decide event, requestNo:{},  decideCommand:{}", decideCommand.getRequestNo(),JSON_PARSER.toJson(decideCommand));
            });
        } catch (Exception e) {
            log.error("error happened when send decide finish event, body:{}", body, e);
        }
    }
    @StopWatchTime
    public void sendConfirmEvent(ConfirmDecideFinishEvent confirmCommand) {
        String body = null;
        try {
            body = JSON_PARSER.toJson(confirmCommand);
            ProducerRecord producerRecord = new ProducerRecord("riskguard_event_async", confirmCommand.getRequestNo(), body);
            producerRecord.headers().add("event-type", "confirm".getBytes(StandardCharsets.UTF_8));
            producer.send(producerRecord, (metadata, exception) -> {
                if (exception != null) {
                    log.error("send decide event failed", exception);
                }
                log.info("send decide event, requestNo:{},  decideCommand:{}", confirmCommand.getRequestNo(),JSON_PARSER.toJson(confirmCommand));
            });
        } catch (Exception e) {
            log.error("error happened when send decide finish event, body:{}", body, e);
        }
    }

}
