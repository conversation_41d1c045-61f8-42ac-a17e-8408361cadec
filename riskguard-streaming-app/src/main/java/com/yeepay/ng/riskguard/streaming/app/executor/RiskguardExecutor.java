/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.app.executor;

import com.yeepay.ng.riskguard.gateway.facade.dto.confirm.ConfirmCommand;
import com.yeepay.ng.riskguard.gateway.facade.dto.confirm.ConfirmExtCommand;
import com.yeepay.ng.riskguard.gateway.facade.dto.confirm.ConfirmResult;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideCommand;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideExtCommand;
import com.yeepay.ng.riskguard.gateway.facade.dto.decide.DecideResult;

/**
 * title: Command执行器<br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/12/26 10:19
 */
public interface RiskguardExecutor {
    /**
     * 问询阶段陪跑
     *
     * @param decideCommand
     * @param decideExtCommand
     * @return
     */
    void decide(DecideCommand decideCommand, DecideExtCommand decideExtCommand);


    /**
     * 确认阶段陪跑
     *
     * @param confirmCommand
     * @param confirmExtCommand
     * @return
     */
    void confirm(ConfirmCommand confirmCommand, ConfirmExtCommand confirmExtCommand);

}
