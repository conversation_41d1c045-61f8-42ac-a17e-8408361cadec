package com.yeepay.ng.riskguard.streaming.app.config;

import com.yeepay.ng.riskguard.engine.app.cache.NameListRefreshableCache;
import com.yeepay.ng.riskguard.engine.app.cache.PolicyNameListRefreshableCache;
import com.yeepay.ng.riskguard.engine.app.executor.impl.PolicyNameListExecutorImpl;
import com.yeepay.ng.riskguard.engine.infrastructure.gateway.NameListConfigGatewayImpl;
import com.yeepay.ng.riskguard.engine.infrastructure.gateway.NameListGatewayImpl;
import com.yeepay.ng.riskguard.engine.spel.CachedSpelExpressionParser;
import com.yeepay.ng.riskguard.engine.spel.SpelExecutor;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * title:
 * description:
 * Copyright: Copyright (c)2025
 * Company: 易宝支付(YeePay)
 * 精准导入需要的类，不再扫描整个包
 *
 * <AUTHOR>
 * @version: 1.0.0
 * @since: 2025/3/13 11:20 上午
 */
@Configuration
@Import({
        SpelExecutor.class,
        CachedSpelExpressionParser.class,
        PolicyNameListExecutorImpl.class,
        PolicyNameListRefreshableCache.class,
        NameListRefreshableCache.class,
        NameListGatewayImpl.class,
        NameListConfigGatewayImpl.class
})
public class SpelConfig {

}
