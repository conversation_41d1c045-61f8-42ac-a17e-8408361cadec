/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ng.riskguard.streaming.app.cache;

import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.ng.riskguard.commons.aspect.StopWatchTime;
import com.yeepay.ng.riskguard.commons.cache.AbstractRefreshCache;
import com.yeepay.ng.riskguard.commons.valueobj.BizScenario;
import com.yeepay.ng.riskguard.rules.facade.rule.dto.event.PolicyVersionUpdateEvent;
import com.yeepay.ng.riskguard.streaming.app.cache.key.PolicyVersionCacheKey;
import com.yeepay.ng.riskguard.streaming.domain.gateway.policy.PolicyVersionDTO;
import com.yeepay.ng.riskguard.streaming.domain.gateway.PolicyGateway;
import com.yeepay.ng.riskguard.commons.cache.YopCacheLoader;
import com.yeepay.ng.riskguard.streaming.domain.gateway.policy.PolicyVersionDetailDTO;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * title: 风控策略配置本地缓存<br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/9 14:00
 */
@Component
@Slf4j
public class PolicyVersionRefreshableCache extends AbstractRefreshCache<PolicyVersionCacheKey, PolicyVersionDTO> {

    @Setter(onMethod_ = @Autowired)
    private PolicyGateway policyGateway;

    @Override
    public YopCacheLoader<PolicyVersionCacheKey, PolicyVersionDTO> getCacheLoader() {
        return new YopCacheLoader<PolicyVersionCacheKey, PolicyVersionDTO>() {
            @Override
            protected PolicyVersionDTO doLoad(PolicyVersionCacheKey policyCacheKey) throws Exception {
                log.info("load policy version, cacheKey:{}", policyCacheKey);
                BizScenario scenario = BizScenario.builder()
                        .bizCode(policyCacheKey.getBizCode())
                        .scenarioCode(policyCacheKey.getScenarioCode())
                        .build();
                List<PolicyVersionDTO> result = policyGateway.queryPolicyVersion(scenario, policyCacheKey.getStageType(), policyCacheKey.getEnvironment());
                return CollectionUtils.isNotEmpty(result) ? result.get(0) : null;
            }

            @Override
            protected String getCacheName() {
                return "PolicyVersionRefreshableCache";
            }
        };
    }

    @Override
    public long refreshAfterWrite() {
        // 缓存刷新时间为1分钟
        return 60;
    }

    @Override
    public long maximumSize() {
        return 1000;
    }

    /**
     * 预热
     *
     * @param cacheKey
     */
    @StopWatchTime
    public void warm(PolicyVersionCacheKey cacheKey) {
        log.info("warn cache, key:{}", cacheKey);
        String bizCode = cacheKey.getBizCode();
        String environment = cacheKey.getEnvironment();
        Set<String> scenarioCodes = new HashSet<>();
        if (StringUtils.isEmpty(cacheKey.getScenarioCode())) {
            List<PolicyVersionDTO> result = policyGateway.queryPolicyVersion(BizScenario.builder()
                    .bizCode(cacheKey.getBizCode()).scenarioCode(cacheKey.getScenarioCode()).build(), cacheKey.getStageType(), cacheKey.getEnvironment());
            if (CollectionUtils.isEmpty(result)) {
                return;
            }
            for (PolicyVersionDTO policyVersionDTO : result) {
                StringBuilder key = new StringBuilder();
                key.append(bizCode).append("_");
                String scenarioCode = policyVersionDTO.getScenarioCode();
                key.append(scenarioCode).append("_");
                Map<String, List<PolicyVersionDetailDTO>> versions = policyVersionDTO.getVersions();
                for (Map.Entry<String, List<PolicyVersionDetailDTO>> entry : versions.entrySet()) {
                    String stage = entry.getKey();
                    key.append(stage).append("_").append(environment);
                    if (scenarioCodes.contains(key.toString())) {
                        log.error("duplicate requestParam config, cacheKey: {}, param: {}", cacheKey, policyVersionDTO);
                    } else {
                        put(PolicyVersionCacheKey.builder()
                                .bizCode(cacheKey.getBizCode())
                                .scenarioCode(scenarioCode)
                                .stageType(stage).environment(environment)
                                .build(), policyVersionDTO);
                        scenarioCodes.add(key.toString());
                    }
                }
            }
        } else {
            refresh(cacheKey);
        }
    }

    @EventListener
    public void onEvent(PolicyVersionUpdateEvent event) {
        log.info("PolicyVersionUpdateEvent received event: {}", event);
        PolicyVersionCacheKey cacheKey = PolicyVersionCacheKey.builder()
                .bizCode(event.getBizScenario().getBizCode())
                .scenarioCode(event.getBizScenario().getScenarioCode())
                .build();
        refresh(cacheKey);
    }
}