<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yeepay.ng.riskguard</groupId>
    <artifactId>riskguard-streaming-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>riskguard-streaming-parent</name>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <jmh.version>1.33</jmh.version>

        <lombok.version>1.18.36</lombok.version>

        <mapstruct.version>1.6.0.Beta2</mapstruct.version>
        <mapstruct-lombok.version>0.2.0</mapstruct-lombok.version>

        <yeepay-boot.version>2.6.4-SNAPSHOT</yeepay-boot.version>
        <yeeworks.version>1.0.0-SNAPSHOT</yeeworks.version>

        <kogito.version>999-SNAPSHOT</kogito.version>
        <kogito.bom.version>999-SNAPSHOT</kogito.bom.version>
        <rest-assured.version>5.3.2</rest-assured.version>
        <drools.version>7.52.0.Final</drools.version>
        <aerospike.version>3.1.6</aerospike.version>
        <kafka.version>3.3.0</kafka.version>
        <flink.version>1.20.0</flink.version>
        <yeepay-utils-common.version>4.2.0</yeepay-utils-common.version>
        <druid.version>1.2.22</druid.version>

        <riskguard-rules-commons.version>1.0.0-SNAPSHOT</riskguard-rules-commons.version>
        <riskguard-rules-facade.version>1.0.0-SNAPSHOT</riskguard-rules-facade.version>
        <riskguard-engine-operator.version>1.0.0-SNAPSHOT</riskguard-engine-operator.version>
        <yeepay-boot-config.version>1.0-SNAPSHOT</yeepay-boot-config.version>
    </properties>

    <modules>
        <module>riskguard-streaming-main</module>
        <module>riskguard-streaming-domain</module>
        <module>riskguard-streaming-infrastructure</module>
        <module>riskguard-streaming-app</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>3.4.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-reload4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yeepay-rmi</artifactId>
                <version>4.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.yeepay.g3.starter</groupId>
                <artifactId>yeepay-soa-starter</artifactId>
                <version>4.0.3.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yeepay-soa</artifactId>
                <version>4.0</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yeepay-utils-common</artifactId>
                <version>${yeepay-utils-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yeepay.ng.riskguard</groupId>
                <artifactId>riskguard-gateway-facade</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.yeepay.ng.riskguard</groupId>
                <artifactId>riskguard-engine-facade</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.yeepay.ng.riskguard</groupId>
                <artifactId>riskguard-rules-commons</artifactId>
                <version>${riskguard-rules-commons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.ng.riskguard</groupId>
                <artifactId>riskguard-rules-facade</artifactId>
                <version>${riskguard-rules-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.ng.riskguard</groupId>
                <artifactId>riskguard-engine-operator</artifactId>
                <version>${riskguard-engine-operator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.ng.riskguard</groupId>
                <artifactId>riskguard-engine-app</artifactId>
                <version>${riskguard-engine-operator.version}</version>
            </dependency>

            <!--Spring Boot-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-logging</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Yeepay boot -->
            <dependency>
                <groupId>com.yeepay.boot</groupId>
                <artifactId>yeepay-boot-dependencies</artifactId>
                <version>${yeepay-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Yeeworks -->
            <dependency>
                <groupId>com.yeepay.ng.yeeworks</groupId>
                <artifactId>yeeworks-parent</artifactId>
                <version>${yeeworks.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Project modules -->
            <dependency>
                <groupId>com.yeepay.ng.riskguard</groupId>
                <artifactId>riskguard-streaming-entry</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.ng.riskguard</groupId>
                <artifactId>riskguard-streaming-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.ng.riskguard</groupId>
                <artifactId>riskguard-streaming-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yeepay.ng.riskguard</groupId>
                <artifactId>riskguard-streaming-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- Project modules End -->

            <!-- Misc -->
            <dependency>
                <groupId>com.yeepay.g3.event</groupId>
                <artifactId>yeepay-event</artifactId>
                <version>4.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>io.rest-assured</groupId>
                <artifactId>rest-assured</artifactId>
                <version>${rest-assured.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- Misc End -->
            <dependency>
                <groupId>com.yeepay.boot.components</groupId>
                <artifactId>yeepay-boot-config</artifactId>
                <version>${yeepay-boot-config.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <failOnError>true</failOnError>
                        <verbose>true</verbose>
                        <fork>true</fork>
                        <compilerArgument>-nowarn</compilerArgument>
                        <compilerArgument>-XDignore.symbol.file</compilerArgument>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>UTF-8</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>${mapstruct-lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.openjdk.jmh</groupId>
                                <artifactId>jmh-generator-annprocess</artifactId>
                                <version>${jmh.version}</version>
                            </path>
                            <path>
                                <groupId>com.tngtech.archunit</groupId>
                                <artifactId>archunit-junit4</artifactId>
                                <version>0.23.1</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                    <configuration>
                        <skip>true</skip>
                    </configuration>
                </plugin>
                <!--<plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.0.0</version>
                    <configuration>
                        <argLine>
                            &#45;&#45;add-opens=java.base/java.lang=ALL-UNNAMED
                            &#45;&#45;add-opens=java.base/java.util=ALL-UNNAMED
                        </argLine>
                    </configuration>
                </plugin>-->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.6.1</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <distributionManagement>
        <repository>
            <id>YeepayReleases</id>
            <name>Internal Releases</name>
            <url>http://artifact.paas.yp:8000/artifactory/yp3g-subsystem-release/</url>
        </repository>
        <snapshotRepository>
            <id>YeepaySnapshots</id>
            <name>Internal Snapshots</name>
            <url>http://artifact.paas.yp:8000/artifactory/yp3g-subsystem-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
